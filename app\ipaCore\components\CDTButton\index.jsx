import React, { useState } from 'react';
import PropTypes from 'prop-types';
import Button from '@mui/material/Button';
import { styled } from '@mui/material/styles';

const StyledButton = styled(Button)(({ theme, color, hoverColor, borderColor, fontSize, borderRadius }) => ({
    borderColor: borderColor || theme.palette.primary.main,
    color: color || theme.palette.primary.main,
    backgroundColor: 'transparent',
    borderRadius: borderRadius,
    textTransform: 'none',
    transition: 'all 0.3s ease',
    fontSize: fontSize,
    '&:hover': {
        color: 'white',
        borderColor: hoverColor || '#023B67',
        backgroundColor: hoverColor || '#023B67',
    }
}));

const CDTButton = (props) => {
    const [isButtonHovered, setIsButtonHovered] = useState(false);
    const dVariant = props.defaultVariant || "contained";
    const hVariant = props.hoverVariant || "outlined";
    return (
        <StyledButton
            variant={isButtonHovered ? dVariant : hVariant}
            onMouseEnter={() => setIsButtonHovered(true)}
            onMouseLeave={() => setIsButtonHovered(false)}
            onClick={props?._onclick}
            color={props.color}
            hoverColor={props.hoverColor}
            borderColor={props.borderColor}
            fontSize={props.fontSize}
            {...props}
        >
            {props.btnName}
        </StyledButton>
    );
};

CDTButton.propTypes = {
    btnName: PropTypes.string.isRequired,
    _onclick: PropTypes.func.isRequired,
    color: PropTypes.string,
    hoverColor: PropTypes.string,
    borderColor: PropTypes.string,
    fontSize: PropTypes.string,
    defaultVariant: PropTypes.string,
    hoverVariant: PropTypes.string,
    borderRadius: PropTypes.string,
};

export default CDTButton;