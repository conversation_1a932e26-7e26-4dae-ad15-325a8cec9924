const ipaConfig = {
  appName: "AI Powered Digital Twin",
  configUserType: "CAT_DigitalTwin_Experience",
  applicationId: "03df473a-17d6-416a-8d84-42f8cd32239f",
  scriptPlugins: [],
  css: [],
  redux: {
    slices: [
      {
        name: "MQTTOps<PERSON>lice", file: "MqttOpsSlice.js"
      },
      {
        name: "sharedDataSensorSlice", file: "SensorSlice.js"
      }
    ],
  },
  components: {
    dashboard: [],
    entityData: [],
    entityAction: [],
  },
};

export default ipaConfig;
