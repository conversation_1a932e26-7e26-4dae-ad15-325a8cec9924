import React, { useContext, useEffect, useRef, useState } from "react";
import { IafViewerDBM } from '@dtplatform/iaf-viewer';
import { ModelContext, ModelContextProvider } from "./ModelContext";
import { StackableDrawer } from '@invicara/ipa-core/modules/IpaControls';
import useConnectMQTTAndRetriveLatestData from "../hooks/useActiveMqConfiguration";
import Loader from "../utils/Loader";
import { useSelector } from "react-redux";
import { MODEL_MACHINE_STATUS, modelElesColorGroups, MODELVIWER_LOADER_TIME } from "../utils/constants";
import { retriveElementPackageIds } from "../utils/common-utils";
import SimulationOps from "./SimulationOps";
import PageHeader from "../Helpers/PageHeader";
import TelemetryUpdate from "./ModelElements/TelemetryUpdate";
import SelectedElementModel from "./ModelElements/SelectedElementModel";
import Notifications from "../notifications/Notifications";

const BIMModelViewer = (props) => {
    return (
        <ModelContextProvider >
            <ModelViwer props={props} />
        </ModelContextProvider>
    )
}
const ModelViwer = ({ props }) => {
    const [isPanelOpen, setPanelOpenstatus] = useState(false);
    const { currentReadings, mqttRunningClient } = useConnectMQTTAndRetriveLatestData();
    const { history } = props;
    const [statusOfEleModelOpen, setStatusOfEleModelOpen] = useState(false);
    const [simulationPanelOpen, setSimulationPanelOpen] = useState(false);
    const viewerRef = useRef(null);
    const urlParams = new URLSearchParams(history.location.search);
    const modelCompositeId = urlParams.get('modelId');
    const [isLoading, setIsLoading] = useState(false);
    const [isDraggablePanelOpen, setIsDraggablePanelOpen] = useState(true);
    const [eleColorGroups, seteleColorGroups] = useState(modelElesColorGroups);
    //Info: Realtime Readings Data
    const { telemetryDevicesWithLatestReading } = useSelector(state => state.MQTTOpsSlice);

    const {
        selectedModelComposite,
        selectedElement,
        sensorElementDisplay,
        getSelectedElement,
        setSelectedPropRefs,
        sliceElements,
        availableModelComposites,
        setSelectedModelComposite,
        setSensorElementDisplay
    } = useContext(ModelContext)

    useEffect(() => {
        setIsLoading(true);
        if (availableModelComposites) {
            const modelToRender = availableModelComposites.find(m => m._id === modelCompositeId);
            setSelectedModelComposite(modelToRender || null);
        }
    }, [availableModelComposites])

    useEffect(() => {
        let timeout;
        if (selectedModelComposite) {
            //Info: The settimeout is to keep loader visble till 3D model loads properly
            timeout = setTimeout(() => {
                setIsLoading(false);
            }, MODELVIWER_LOADER_TIME)
        }
        return () => {
            clearTimeout(timeout);
        }
    }, [selectedModelComposite])

    useEffect(() => {
        setIsDraggablePanelOpen(true);
    }, [selectedElement])

    // Info: To Track reading changes
    useEffect(() => {
        analyzeStateAndHighlightElement();
    }, [telemetryDevicesWithLatestReading])
    // Info: Analysis of mechine status and do color codiong as per status
    const analyzeStateAndHighlightElement = () => {
        let { modelElesWithRunning, modelElesWithStopped,modelElesWithAuto } = retriveElementPackageIds(telemetryDevicesWithLatestReading);
        seteleColorGroups((prev) => {
            try {
                let newColorGrouo = prev.map(item => {
                    let { colors } = item;
                    const newColor = colors.map(cl => {
                        switch (cl?.title) {
                            case MODEL_MACHINE_STATUS.stopped:
                                return { ...cl, elementIds: modelElesWithStopped }
                            case MODEL_MACHINE_STATUS.auto:
                                return { ...cl, elementIds: modelElesWithAuto }
                            // Info: Commented for nowm if in future more status will be added to it will need to be opned to track that as well
                            // case MODEL_MACHINE_STATUS.running:
                            //     return { ...cl, elementIds: modelElesWithRunning }
                            default:
                                return { ...cl, elementIds: [] }
                        }
                    })
                    return { ...item, colors: newColor || [] }
                })
                return newColorGrouo;
            } catch (e) {
                //Info: Add error toster
                alert('Error on applying color Coding with 3D model!!');
            }

        })
    }

    return (
        <>
            <Notifications />
            <PageHeader pageText={selectedModelComposite?._name || '3D Space'} showBackButton={true} goToHome={false} />
            <div className='viewer'>
                {/* Info:Hide for now, will be opened in future */}
                {/* <div className={statusOfEleModelOpen ? 'model-info-sidebar drawerOpen' : 'model-info-sidebar'}>
                    <StackableDrawer level={1} iconKey='fa-info' tooltip='BIM Info' isDrawerOpen={false} onOpen={() => setStatusOfEleModelOpen(true)} onClose={() => setStatusOfEleModelOpen(false)}>
                        {selectedElement && <ElementDetails element={selectedElement} horizontal={false} />}
                    </StackableDrawer>
                </div> */}
                <div className={simulationPanelOpen ? 'simulation-operation-sidebar drawerOpen' : 'simulation-operation-sidebar'}>
                    <StackableDrawer level={2} iconKey={!simulationPanelOpen ? 'fa-sliders' : 'fa-xmark'} tooltip='Simulation Settings' isDrawerOpen={false} onOpen={() => setSimulationPanelOpen(true)} onClose={() => setSimulationPanelOpen(false)}>
                        <SimulationOps mqqtClient={mqttRunningClient} />
                    </StackableDrawer>
                </div>
                {modelCompositeId && isLoading && <Loader />}
                {selectedModelComposite && <IafViewerDBM
                    ref={viewerRef} model={selectedModelComposite}
                    serverUri={"https://sandbox-api.invicara.com"}
                    sliceElementIds={sliceElements.map(se => [se.package_id, se.source_id]).flat()}
                    selection={selectedElement ? [selectedElement.package_id, selectedElement.source_id] : []}
                    colorGroups={eleColorGroups}
                    OnSelectedElementChangeCallback={getSelectedElement}
                />}
            </div>
            {/* Info: Commented for now, will be opened in future */}
            {/* {selectedElement &&
                <DraggablePanel
                    isPanelOpen={selectedElement && isDraggablePanelOpen}
                    position={{ left: 60, top: 40 }}
                    handlePanelClose={() => setIsDraggablePanelOpen(false)}>
                    <ModelTelemetryElelements selectedElement={selectedElement?.package_id || null} handlePanelClose={() => setSensorElementDisplay(false)}/>
                </DraggablePanel>
            } */}

            {!sensorElementDisplay ? <TelemetryUpdate telemetryDeviceReading={telemetryDevicesWithLatestReading} /> : <SelectedElementModel selectedElementPackageId={selectedElement?.package_id || null} handlePanelClose={() => setSensorElementDisplay(false)} />}
        </>
    )
}

export default BIMModelViewer;