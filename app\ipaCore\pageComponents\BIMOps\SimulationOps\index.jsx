import Button from '@mui/material/Button';
import React, { memo, useEffect, useState } from "react";
import { publishMessageOnMqtt } from "../../utils/common-utils";
import { IafItemSvc } from "@dtplatform/platform-api";
import { useSelector } from "react-redux";
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import ListItemText from '@mui/material/ListItemText';
import Select from '@mui/material/Select';
import Checkbox from '@mui/material/Checkbox';
import { MODEL_MACHINE_STATUS, SIMULATION_OPS, SIMULATION_OPS_TEXTCONTENT } from '../../utils/constants';
import ToastComponent from '../../utils/ToastComponent';

const SimulationOps = ({ mqqtClient }) => {
    const { telemetryDevicesWithLatestReading } = useSelector((state) => state.MQTTOpsSlice);
    const [machineToDetectDistubFeed, setMachineTODetectDisturbFeed] = useState('');
    const [machineToCreateFault, setMachineToCreateFault] = useState([]);
    const [machineToResolveFault, setMachineToResolveFault] = useState([]);
    const performSimulationOperation = (type) => {
        switch (type) {
            case 'disturbFeed':
                const messageDF = { machines: [machineToDetectDistubFeed], task: SIMULATION_OPS.disturbFeed }
                publishMessageOnMqtt(mqqtClient, process.env.TOPIC_TO_PUBLISH_SIMULATION_TASKS || '', messageDF);
                setMachineTODetectDisturbFeed('')
                break;
            case 'createFault':
                const messageCF = { machines: machineToCreateFault, task: SIMULATION_OPS.createFault }
                publishMessageOnMqtt(mqqtClient, process.env.TOPIC_TO_PUBLISH_SIMULATION_TASKS || '', messageCF);
                setMachineToCreateFault([])
                break;
            case 'resolveFault':
                const messageRF = { machines: machineToResolveFault, task: SIMULATION_OPS.resolveFault }
                publishMessageOnMqtt(mqqtClient, process.env.TOPIC_TO_PUBLISH_SIMULATION_TASKS || '', messageRF);
                setMachineToResolveFault([]);
                break;
            case 'systemAuto':
                const messageSA = { machines: ['CNC1'],task: SIMULATION_OPS.autoSystem }
                publishMessageOnMqtt(mqqtClient, process.env.TOPIC_TO_PUBLISH_SIMULATION_TASKS || '', messageSA);
                break;
        }
    }

    return (
        <>
            <div className="simulationops-wrapper">
                <div className="operation-tbs">
                    <h5 className='drawer-head'>{SIMULATION_OPS_TEXTCONTENT.SIMULATION_OPERATION}</h5>
                    <div className="operation-card">
                        <p className="operation-heading">{SIMULATION_OPS_TEXTCONTENT.DISTURBFEED}</p>
                        <div className="formwrap">
                            <FormControl fullWidth>
                                <InputLabel id="demo-simple-select-label">{SIMULATION_OPS_TEXTCONTENT.MACHINE}</InputLabel>
                                <Select
                                    value={machineToDetectDistubFeed}
                                    label={SIMULATION_OPS_TEXTCONTENT.DISTURBFEED}
                                    onChange={(e) => setMachineTODetectDisturbFeed(e.target.value)}
                                >
                                    {telemetryDevicesWithLatestReading && telemetryDevicesWithLatestReading.map((item) => (
                                        item._sensorType === SIMULATION_OPS_TEXTCONTENT.CNC_MACHINE &&
                                        <MenuItem value={item._sourceId}>{item._name}</MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </div>
                        <Button variant="contained" color="primary" onClick={() => performSimulationOperation('disturbFeed')}>Disturb Feed</Button>
                    </div>
                    <div className="operation-card">
                        <p className="operation-heading">{SIMULATION_OPS_TEXTCONTENT.GENERATE_FAULT}</p>
                        <div className="formwrap">
                            <FormControl fullWidth>
                                <InputLabel id="demo-simple-select-label">{SIMULATION_OPS_TEXTCONTENT.MACHINES}</InputLabel>
                                <Select
                                    value={machineToCreateFault}
                                    label={SIMULATION_OPS_TEXTCONTENT.CREATE_FAULT}
                                    multiple
                                    renderValue={(selected) => selected.join(', ')}
                                    onChange={(e) => setMachineToCreateFault(typeof e.target.value === 'string' ? e.target.value.split(',') : e.target.value)}
                                >
                                    {telemetryDevicesWithLatestReading && telemetryDevicesWithLatestReading.filter(item => item?.readings["MachineStatus"] === MODEL_MACHINE_STATUS.running).map((item) => (
                                        <MenuItem value={item._sourceId}>
                                            <Checkbox checked={machineToCreateFault.includes(item._sourceId)} />
                                            <ListItemText primary={item._name} />
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </div>
                        <Button variant="contained" color="primary" onClick={() => performSimulationOperation('createFault')}>{SIMULATION_OPS_TEXTCONTENT.CREATE_FAULT}</Button>
                    </div>
                    <div className="operation-card">
                        <p className="operation-heading">{SIMULATION_OPS_TEXTCONTENT.RESOLVE_FAULT}</p>
                        <div className="formwrap">
                            <FormControl fullWidth>
                                <InputLabel id="demo-simple-select-label">{SIMULATION_OPS_TEXTCONTENT.MACHINES}</InputLabel>
                                <Select
                                    value={machineToResolveFault}
                                    label={SIMULATION_OPS_TEXTCONTENT.RESOLVE_FAULT}
                                    multiple
                                    renderValue={(selected) => selected.join(', ')}
                                    onChange={(e) => setMachineToResolveFault(typeof e.target.value === 'string' ? e.target.value.split(',') : e.target.value)}
                                >
                                    {telemetryDevicesWithLatestReading && telemetryDevicesWithLatestReading.filter(item => item?.readings["MachineStatus"] === MODEL_MACHINE_STATUS.stopped).map((telItem) => (
                                        <MenuItem value={telItem._sourceId}>
                                            <Checkbox checked={machineToResolveFault.includes(telItem._sourceId)} />
                                            <ListItemText primary={telItem._name} />
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </div>
                        <Button variant="contained" color="primary" onClick={() => performSimulationOperation('resolveFault')}>{SIMULATION_OPS_TEXTCONTENT.RESOLVE_FAULT}</Button>
                    </div>
                    <div className="operation-card">
                        <p className="operation-heading">{SIMULATION_OPS_TEXTCONTENT.MAKE_SYSTEM_AUTO}</p>
                        <Button variant="contained" color="primary" onClick={() => performSimulationOperation('systemAuto')}>{SIMULATION_OPS_TEXTCONTENT.SYSTEM_AUTO}</Button>
                    </div>
                </div>
            </div>
        </>
    )
}

export default memo(SimulationOps);