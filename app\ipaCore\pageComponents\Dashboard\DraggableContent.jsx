import React, { useState, useEffect } from 'react'
import { LineChart } from '@mui/x-charts/LineChart';
import _ from 'lodash';
import ErrorIcon from '@mui/icons-material/Error';
import { publishMessageOnMqtt } from '../utils/common-utils';
import useConnectMQTTAndRetriveLatestData from "../hooks/useActiveMqConfiguration";
import { DASH_CARDS, MODEL_MACHINE_STATUS, NOTIFICATION, SIMULATION_OPS } from '../utils/constants';
import { useDispatch } from 'react-redux';
import { removeAnomalyNotificationOnManualStop } from '../../redux/MqttOpsSlice';
import { Button } from '@mui/material';

const DraggableContent = ({ data, filteredMachine, allFilteredData, isPanelOpen }) => {
    const { mqttRunningClient } = useConnectMQTTAndRetriveLatestData();
    const [isButtonDisable, setDisableButton] = useState(false);
    const [isButtonClicked, setButtonClicked] = useState({ StopMachine: false, ResolveFault: false });
    const dispatch = useDispatch();
    // Info :: Stop Machine button logic


    const handleStopMachine = (machineToStop) => {
        setButtonClicked({ ...isButtonClicked, StopMachine: true });
        const { readings } = machineToStop;
        if (readings) {
            // Info : For future use
            // let notificationToRemove = {
            //     _sourceId: readings?._sourceId,
            //     _anomaly: readings['Anomaly'],
            //     _status: readings['MachineStatus'],
            //     _responsibleParamters: [],
            //     _ts: readings?._ts
            // }

            const messageCF = { machines: [readings?._sourceId], task: SIMULATION_OPS.createFault }
            publishMessageOnMqtt(mqttRunningClient, process.env.TOPIC_TO_PUBLISH_SIMULATION_TASKS || '', messageCF);
            // dispatch(removeAnomalyNotificationOnManualStop(notificationToRemove));
            setDisableButton(true)
        }


    }

    const handleResolveMachine = (machineToStop) => {
        const { readings } = machineToStop;
        setButtonClicked({ ...isButtonClicked, ResolveFault: true });
        const messageRF = { machines: [readings?._sourceId], task: SIMULATION_OPS.resolveFault }
        publishMessageOnMqtt(mqttRunningClient, process.env.TOPIC_TO_PUBLISH_SIMULATION_TASKS || '', messageRF);
        setDisableButton(true)

    }

    useEffect(() => {
        if (!isPanelOpen) {
            setButtonClicked({ StopMachine: false, ResolveFault: false });
        }
    }, [isPanelOpen]);



    const renderMachineStatusAction = () => {
        if (data.readings.MachineStatus === MODEL_MACHINE_STATUS.stopped && data.readings.Anomaly === MODEL_MACHINE_STATUS.hasAnomaly) {
            return (
                <Button
                    variant="contained"
                    color="primary"
                    onClick={() => handleResolveMachine(data)}
                    disabled={isButtonClicked.ResolveFault}
                >
                    {DASH_CARDS.RESOLVE_FAULT}
                </Button>
            );
        } else if (data.readings.MachineStatus === MODEL_MACHINE_STATUS.auto) {
            return (
                <div className='automachine-info'>
                    {DASH_CARDS.AUTO_STMT1}
                    <br />
                    {DASH_CARDS.AUTO_STMT2}
                </div>
            );
        } else {
            return (
                <Button
                    variant="contained"
                    color="primary"
                    onClick={() => handleStopMachine(data)}
                    disabled={isButtonClicked.StopMachine}
                >
                    {NOTIFICATION.STOP_MACHINE}
                </Button>
            );
        }
    };

    return (
        <div className="drag-wrapper">
            <div className='drag-status'><ErrorIcon className="error-icon" />
                {NOTIFICATION.ANOMALY_DETECTED}</div>
            <div className="readingsWrp">
                <span className="drag-ele-name">{NOTIFICATION.MACHINE_NAME}{data?._name}</span>
                <div className="drag-values">
                    {!_.isEmpty(allFilteredData) ? <LineChart
                        height={260}
                        series={[
                            {
                                ...allFilteredData,
                                line: {
                                    marker: {
                                        visible: false,
                                    },
                                },
                            },
                        ]}
                        xAxis={[{ scaleType: 'point', data: filteredMachine[0].graphs[0]['graphXAxis'] }]}
                        yAxis={[{ width: 50 }]}
                    /> : <div>{NOTIFICATION.NO_DATA_FOUND}</div>}
                </div>
                {renderMachineStatusAction()}
            </div>

        </div>
    )
}

export default DraggableContent