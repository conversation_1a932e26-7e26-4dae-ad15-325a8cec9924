import React, { useState, useMemo, useEffect } from 'react';
import {
    Container,
    Card,
    CardContent,
    Typography,
    Grid,
    Box,
    Button, Checkbox, FormControlLabel, CardMedia
} from '@mui/material';
import '@Scss/index.scss';
import ModelImage from '../../sass/assets/model-image.png';
import MachineImage from '../../sass/assets/machine-view.jpg';
import WeldingRobot from '../../sass/assets/Welding_Robot.png';
import useConnectMQTTAndRetriveLatestData from '../hooks/useActiveMqConfiguration';
import { useSelector } from 'react-redux';
import { DataPointsToShow, ParamEtersUnits, DASH_CARDS } from '../utils/constants';
import Loader from '../utils/Loader';
import _ from 'lodash';
import { LineChart } from '@mui/x-charts/LineChart';
import { useHistory } from 'react-router-dom';
import PageHeader from '../Helpers/PageHeader';
import TimestampFormatter from '../utils/TimestampFormatter';
import Notifications from '../notifications/Notifications';



const Dashboard = ({ history }) => {
    const navHistory = useHistory();
    // TODO: Will be used in future
    const { currentReadings } = useConnectMQTTAndRetriveLatestData();
    const { telemetryDevicesWithLatestReading, isFetchingTelItems, machineGraphs } = useSelector(state => state.MQTTOpsSlice)

    const [runningChecked, setRunningChecked] = useState(false);
    const [stoppedChecked, setStoppedChecked] = useState(false);
    const [sourceId, setSourceId] = useState(null);

    const urlParams = new URLSearchParams(history.location.search);
    const modelCompositeId = urlParams.get('modelId');

    const filteredData = useMemo(() => (telemetryDevicesWithLatestReading.filter((item) => {
        if (runningChecked && stoppedChecked) {
            return item?.readings?.MachineStatus === 'Running' || item?.readings?.MachineStatus === 'Stopped';
        }
        if (runningChecked) {
            return item?.readings?.MachineStatus === 'Running';
        }
        if (stoppedChecked) {
            return item?.readings?.MachineStatus === 'Stopped';
        }
        return true;
    })), [runningChecked, stoppedChecked, telemetryDevicesWithLatestReading])


    const handleOnCardClick = (sourceId) => {
        setSourceId(sourceId)
    }

    const filteredMachine = machineGraphs?.filter(item => item?._sourceId === sourceId);


    let allFilteredData = filteredMachine.length && filteredMachine[0]?.graphs?.map((item) => {
        return { data: item['graphYAxis']?.filter((item) => typeof item === "number"), label: item['graphParameter'] }
    })



    useEffect(() => {
        filteredData && setSourceId(filteredData[0]?._sourceId || null)
    }, [isFetchingTelItems, runningChecked, stoppedChecked])

    return (
        <>
            {(!isFetchingTelItems) ?

                <>

                    <Notifications />
                    <Container maxWidth={false} className='main-container'>
                        <PageHeader pageText={DASH_CARDS.DASHBOARD} showBackButton={true} goToHome={true} />
                        <Grid container spacing={3}>
                            <Grid item xs={12} md={7} className='left-sections'>
                                {/* Model Overview */}
                                <Card variant="outlined" className='left-section1'>
                                    <CardContent className='model-card'>
                                        <div className='model-head-section'>
                                            <Typography className='model-title'>
                                                {DASH_CARDS.MODEL_OVERVIEW}
                                            </Typography>
                                            <Button size="small" onClick={() => navHistory.push(`/model-viewer?modelId=${modelCompositeId}`)}>
                                                {DASH_CARDS.VIEW_MODEL}
                                            </Button>
                                        </div>
                                        <Box
                                            component="img"
                                            src={ModelImage}
                                            alt={DASH_CARDS.MODEL_IMAGE}
                                            className='model-image'
                                        />
                                    </CardContent>
                                </Card>

                                {/* Real-Time Sensor Readings */}
                                <Card variant="outlined">
                                    <CardContent>
                                        <Typography className='model-title-graph'>
                                            {DASH_CARDS.REAL_TIME}
                                        </Typography>
                                        <Box
                                            className="chart-wrapper"
                                        >
                                            {!_.isEmpty(filteredMachine[0]) && !_.isEmpty(filteredMachine[0]?.graphs[0]) ? (
                                                <LineChart
                                                    height={308}
                                                    series={allFilteredData}
                                                    xAxis={[{ scaleType: 'point', data: filteredMachine[0]?.graphs[0]?.['graphXAxis'] }]}
                                                    yAxis={[{ width: 50 }]}
                                                />
                                            ) : (
                                                <div className='no-data'>{DASH_CARDS.SELECT_MACHINE}</div>
                                            )}
                                        </Box>
                                    </CardContent>
                                </Card>
                            </Grid>

                            {/* Right side: Model Components */}
                            <Grid item xs={12} md={5}>
                                <Card variant="outlined" className='right-section'>
                                    <CardContent>
                                        <div className='model-head-section'>
                                            <Typography variant="h6" className='model-title'>
                                                {DASH_CARDS.MODEL_COMP}
                                            </Typography>
                                            <Button onClick={() => navHistory.push("sensorManagement")}>{DASH_CARDS.MANAGE_MACHINE}</Button>
                                        </div>
                                        <Box className='status-container'>
                                            <span className='red-dot'></span>
                                            <FormControlLabel
                                                control={<Checkbox checked={stoppedChecked} onChange={(e) => setStoppedChecked(e.target.checked)} />}
                                                label="Stopped"
                                                labelPlacement="start"
                                                className='checkbox-status'
                                            />

                                            {/* Info :: For future use if needed
                                             <span className='red-dot yellow-dot'></span>
                                <FormControlLabel
                                    control={<Checkbox checked={true} onChange={() => { }} />}
                                    label="Idle"
                                    labelPlacement="start"
                                    className='checkbox-status'
                                /> */}
                                            <span className='red-dot blue-dot'></span>
                                            <FormControlLabel
                                                control={<Checkbox checked={runningChecked} onChange={(e) => setRunningChecked(e.target.checked)} />}
                                                label="Running"
                                                labelPlacement="start"
                                                className='checkbox-status'
                                            />
                                        </Box>
                                        <Box className='card-scroll'>
                                            {!_.isEmpty(filteredData) ? filteredData.map((robot, index) => {
                                                const insertClass = robot._sourceId === sourceId ? 'shadow-selected' : '';
                                                return (<Card Card variant="outlined" key={index} className={`model-detail-card ${insertClass}`
                                                } onClick={() => {
                                                    handleOnCardClick(robot._sourceId)
                                                }}>
                                                    <div

                                                        className={`vertical-line ${robot?.readings?.MachineStatus === 'Running' ? 'running-line' : 'stopped-line'}`}
                                                    />
                                                    <CardContent>
                                                        <Box className='heading-container'>
                                                            <Box className='model-text'>
                                                                <Typography className="subtitle1">{robot._name}</Typography>

                                                                {
                                                                    !_.isEmpty(robot.readings) ? Object.keys(robot.readings).map(property => (
                                                                        DataPointsToShow.includes(property) &&
                                                                        <div className='info1' key={property._id}>
                                                                            <span>{property} : </span>
                                                                            <span>
                                                                                {typeof robot?.readings[property] !== 'object' && `${robot?.readings[property]} ${ParamEtersUnits[property] || ""}`}
                                                                            </span>
                                                                        </div>

                                                                    )) : <div className='no-data'>{DASH_CARDS.NO_DATA}</div>}
                                                                {!_.isEmpty(robot?.readings) && <Typography variant="h6" className="info1 date-time">
                                                                    <TimestampFormatter timestamp={robot?.readings._ts} />
                                                                </Typography>}


                                                            </Box >
                                                            <CardMedia
                                                                component="img"
                                                                image={robot._name.includes("Welding") ? WeldingRobot : MachineImage}
                                                                alt="Robot Image"
                                                                className='robot-image'
                                                            />
                                                        </Box>
                                                    </CardContent>
                                                </Card>)
                                            })
                                                : <div className='no-data'>{DASH_CARDS.NO_DATA}</div>}
                                        </Box>
                                    </CardContent>
                                </Card>
                            </Grid>
                        </Grid>
                    </Container >
                </> : (<Loader />)

            }

        </>
    )
}

export default Dashboard