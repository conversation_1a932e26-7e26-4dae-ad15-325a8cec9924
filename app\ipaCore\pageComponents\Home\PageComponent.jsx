import React, { useRef, useState, useEffect } from 'react';
import { CardContent } from "@mui/material";
import Illustration from "@Scss/assets/illustration.svg";
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import NorthEastIcon from '@mui/icons-material/NorthEast';
import FactoryImage from "@Scss/assets/machine.png";

import {
    HomePageBannerContainer,
    BannerFirstSection,
    BannerSecondSection,
    BannerTitle,
    BannerSubtitle,
    BannerSecondSectionTitle,
    BannerSecondSectionSubtitle,
    CardWrapper,
    CardContainer,
    StyledCard,
    IconButtonContainer,
    ArrowButton,
    CardTitle,
    CardImage,
    ScrollIconButtonContainer,
    ScrollIconButtonLeft,
    ScrollIconButtonRight,
    PageWrapper,
} from '@Scss/pages/homePage.style';
import { LANDING_PAGE } from '../utils/constants';
import CDTButton from '../../components/CDTButton';
import { IllustrationImage } from '../../sass/pages/homePage.style';

const PageComponent = ({ models, handleOnModelClick, handleAddModelBtnClick }) => {
    const scrollContainerRef = useRef(null);
    const [isScrollable, setIsScrollable] = useState(false);

    //Info: Adding for now to show only few models
    const modelsToShow = ['Factory Layout1']
    const scrollLeft = () => {
        if (scrollContainerRef.current) {
            scrollContainerRef.current.scrollBy({
                left: -250,
                behavior: 'smooth'
            });
        }
    };

    const scrollRight = () => {
        if (scrollContainerRef.current) {
            scrollContainerRef.current.scrollBy({
                left: 250,
                behavior: 'smooth'
            });
        }
    };

    useEffect(() => {
        const checkScrollable = () => {
            if (scrollContainerRef.current) {
                const containerWidth = scrollContainerRef.current.scrollWidth;
                const viewportWidth = scrollContainerRef.current.offsetWidth;
                setIsScrollable(containerWidth > viewportWidth);
            }
        };

        // Check on mount and resize
        checkScrollable();
        window.addEventListener('resize', checkScrollable);

        return () => {
            window.removeEventListener('resize', checkScrollable);
        };
    }, []);

    return (
        <PageWrapper>
            <div className='position-relative'>
                <HomePageBannerContainer>
                    <BannerFirstSection>
                        <BannerTitle>
                            {LANDING_PAGE.BANNER_TITLE}
                        </BannerTitle>
                        <BannerSubtitle>
                            {LANDING_PAGE.BANNER_SUBTITLE}
                        </BannerSubtitle>
                    </BannerFirstSection>
                </HomePageBannerContainer>
                <BannerSecondSection>
                    <BannerSecondSectionTitle>
                        {LANDING_PAGE.DEPLOYED_MODELS}
                    </BannerSecondSectionTitle>
                    <BannerSecondSectionSubtitle>
                        {LANDING_PAGE.DEPLOYED_MODEL_SUBTEXT}
                    </BannerSecondSectionSubtitle>
                    <CDTButton
                        btnName={LANDING_PAGE.ADD_NEW_MODEL}
                        _onclick={handleAddModelBtnClick}
                        fontSize={'22px'}
                        borderRadius={'1.625rem'}
                        padding={'0.375rem 1.25rem'}
                        className="add-new-model-button"
                    />
                </BannerSecondSection>
                <IllustrationImage src={Illustration} rel="illustration" />
            </div>

            <CardContainer>
                <CardWrapper ref={scrollContainerRef}>
                    {models?.map((layout) => (
                        modelsToShow.includes(layout?._name) &&
                        <StyledCard
                            key={layout?._id}
                            onClick={() => handleOnModelClick(layout?._id)}
                        >
                            <IconButtonContainer>
                                <ArrowButton className='arrow-button'>
                                    <NorthEastIcon fontSize="small" />
                                </ArrowButton>
                            </IconButtonContainer>
                            <CardContent>
                                <CardTitle>
                                    {layout?._name}
                                </CardTitle>
                            </CardContent>
                            <CardImage
                                component={'img'}
                                image={FactoryImage}
                                alt={'factory-model'}
                            />
                        </StyledCard>
                    ))}
                </CardWrapper>

                {isScrollable && (<ScrollIconButtonContainer>
                    <ScrollIconButtonLeft
                        onClick={scrollLeft}
                    >
                        <ArrowBackIcon sx={{ color: '#005ba1' }} />
                    </ScrollIconButtonLeft>
                    <ScrollIconButtonRight
                        onClick={scrollRight}
                    >
                        <ArrowForwardIcon sx={{ color: '#005ba1' }} />
                    </ScrollIconButtonRight>
                </ScrollIconButtonContainer>
                )}
            </CardContainer>
        </PageWrapper>
    );
}

export default PageComponent;
