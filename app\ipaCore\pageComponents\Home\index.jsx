import React, { useEffect, useState } from 'react'
import { Container } from '@mui/material';
import { IafProj } from '@dtplatform/platform-api';
import Loader from '../utils/Loader';
import PageComponent from './PageComponent';
import { HomePageContainers } from '../../sass/pages/homePage.style';

const HomePage = ({ history }) => {
  const [modelAvailable, setModelAvailable] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadAllModels()
  }, []);

  const loadAllModels = async () => {
    try {
      let currentProject = await IafProj.getCurrent()
      let importedModelComposites = await IafProj.getModels(currentProject)
      setModelAvailable(importedModelComposites)
      setLoading(false)
    } catch (err) {
      console.error("ERROR: Retrieving Imported Models")
      console.error(err)
    }
    finally {
      setLoading(false)
    }
  }

  const handleOnModelClick = async (modelId) => {
    history.push(`/dashboard?modelId=${modelId}`);
  }

  const handleAddModelBtnClick = async () => {
    history.push(`/bimOps`);
  }

  return (
    <HomePageContainers maxWidth="false">
      <> {loading ? <Loader /> :
        <PageComponent models={modelAvailable} handleOnModelClick={handleOnModelClick} handleAddModelBtnClick={handleAddModelBtnClick} />}
      </>
    </HomePageContainers>
  )
}

export default HomePage