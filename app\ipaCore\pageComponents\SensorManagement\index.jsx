import React, { useCallback, useEffect, useState } from 'react'
import { getListOfNamedUserCollections } from '@AdminServices/ItemService'
import { SENSOR_MANAGEMENT_SCREEN } from '../utils/constants'
import MUITable from '../../components/MUITable';
import VisibilityIcon from '@mui/icons-material/Visibility';
import { GridActionsCellItem } from '@mui/x-data-grid';
import { Container } from '@mui/material';
import PageHeader from '../Helpers/PageHeader';
import { useDispatch } from 'react-redux';
import { setSensorList } from '../../redux/SensorSlice'
import '@Scss/viewer.scss';

const INITIAL_STATE = {
  namedUserItemsList: [],
  filteredResult: [],
  isLoading: false,
  searchQuery: ''
};

export default function SensorManagement(props) {
  const [state, setState] = useState(INITIAL_STATE)
  const dispatch = useDispatch()

  const { history } = props

  useEffect(() => {
    (async () => {
      setState(prev => ({ ...prev, isLoading: true }))
      getAllNamedUserItemList()
    })()
  }, [])

  const createColumns = [
    {
      field: '_id',
      headerName: 'ID',
      align: 'center',
      flex: 1,
      headerAlign: 'center',
    },
    {
      field: '_name',
      headerName: 'Name',
      type: 'text',
      align: 'left',
      flex: 1,
      headerAlign: 'left',
    },
    {
      field: '_itemClass',
      headerName: 'Item Class',
      type: 'text',
      align: 'left',
      flex: 1,
      headerAlign: 'left',
    },
    {
      field: '_userType',
      headerName: 'User Type',
      type: 'text',
      align: 'left',
      flex: 1,
      headerAlign: 'left',
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      flex: 1,
      cellClassName: 'actions',
      align: 'center',
      sortable: false,
      headerAlign: 'center',
      getActions: ({ id, row }) => [
        <GridActionsCellItem
          key={`view-${id}`}
          icon={<VisibilityIcon color="primary" />}
          label="View"
          onClick={() => handleSettingIcon(row)}
          color="inherit"
        />
      ],
    }
  ];

  const getAllNamedUserItemList = async () => {
    try {
      const list = await getListOfNamedUserCollections({ query: { _itemClass: 'NamedTelemetryCollection' } })
      const updatedList = list.map((item, index) => ({
        ...item,
        id: item?._id || index,
        _name: item?._name || 'NA',
        _itemClass: item?._itemClass || 'NA',
        _userType: item?._userType || 'NA',
      }));
      setState(prev => ({
        ...prev,
        namedUserItemsList: updatedList || [],
        filteredResult: updatedList || [],
        isLoading: false
      }));
    } catch (e) {
      console.error(e)
      setState(prev => ({
        ...prev,
        namedUserItemsList: [],
        filteredResult: [],
        isLoading: false
      }));
    }
  }

  const handleSettingIcon = (item) => {
    localStorage.setItem("sensorList",JSON.stringify(item))
    dispatch(setSensorList(item))
    history.push(`/sensorList`)
  }

  const handleSearchValue = useCallback((event) => {
    const searchValue = event.target.value.toLowerCase();
    setState(prev => ({
      ...prev,
      searchQuery: searchValue,
      filteredResult: prev.namedUserItemsList?.filter(item =>
        item._name.toLowerCase().includes(searchValue)
      )
    }));
  }, []);

  return (
    <Container maxWidth="false">
      <PageHeader showBackButton={true} pageText={SENSOR_MANAGEMENT_SCREEN.SENSOR_MANAGEMENT} />
      <MUITable
        title={SENSOR_MANAGEMENT_SCREEN.TELEMETRY_COLLECTION}
        rows={state.filteredResult}
        columns={createColumns}
        getRowId={(row) => row._id}
        handleChange={handleSearchValue}
        isLoading={state.isLoading}
        buttons={[]}
        showSearchBar={true}
      />
    </Container>
  )
}