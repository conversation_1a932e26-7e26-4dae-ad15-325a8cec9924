import React, { useEffect, useState } from "react";
import { Modal } from "react-bootstrap";
import { useForm } from "react-hook-form";
import { IafItemSvc } from "@dtplatform/platform-api";
import CommonPopup from '../utils/CommonPopup';
import ToastComponent from "../utils/ToastComponent";
import Button from "../utils/Button";
import '@Scss/viewer.scss';
import CloseIcon from '@mui/icons-material/Close';
import { object } from "prop-types";
import { SENSOR_MANAGEMENT_SCREEN, MODEL, COMMON_POPUP_TEXT, TOASTER_MESSAGE } from '../utils/constants'
import PageHeader from "../Helpers/PageHeader";
import { Container } from "@mui/material";
import VisibilityIcon from '@mui/icons-material/Visibility';
import MUITable from "../../components/MUITable";
import Delete from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import { GridActionsCellItem } from "@mui/x-data-grid";
import { useSelector, useDispatch } from "react-redux";
import { setSensorDetails } from "../../redux/SensorSlice";
import _ from "lodash";

export default function TelemetryCollection(props) {
  const [isEditMode, setIsEditMode] = useState(false);
  const [telItemsList, setTelItemsList] = useState([]);
  const [modalShow, setModalShow] = useState(false);
  const [sensorsCollection, setSensorsCollection] = useState(null);
  const [currentItemToEdit, setCurrentItemToEdit] = useState();
  const [successMessage, setSuccessMessage] = useState(false);
  const [toasterType, setToasterType] = useState("")
  const [toasterMessage, setToasterMessage] = useState("")
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
  const [telToDelete, setTelToDelete] = useState(null);
  const [isLoading, setIsLoading] = useState(false)
  const [delAllSensors, setDelAllSensors] = useState(false)
  const sensorDetails = useSelector((state) => state.sharedDataSensorSlice.sensorList) ?? JSON.parse(localStorage.getItem('sensorList'))

  const dispatch = useDispatch()

  const { history } = props

  const {
    register,
    handleSubmit,
    formState: { errors },
    resetField,
  } = useForm();

  useEffect(() => {
    if (!_.isEmpty(sensorDetails)) {
      setIsLoading(true);
      setSensorsCollection(sensorDetails);
      getTelItems();
    } else {
      setIsLoading(true)
    }

  }, []);

  const getTelItems = async () => {
    setIsLoading(true)
    const dataQuery = { query: {} };
    try {
      const res = await IafItemSvc.getRelatedItems(sensorDetails._id, dataQuery);
      const updatedList = res._list.map((item, index) => ({
        ...item,
        id: item?._id || index,
      }));
      setTelItemsList(updatedList);
      setIsLoading(false);
    } catch (e) {
      console.error(e);
      setTelItemsList([]);
      setIsLoading(false);
    }
  };

  const handleCloseModal = (e) => {
    setIsEditMode(false);
    setModalShow(false);
    resetField("sourceId");
    resetField("name");
    resetField("description");
    resetField("sensorType");
    resetField("modelLocationElementId");
  };

  const buttonToAdd = [
    {
      label: SENSOR_MANAGEMENT_SCREEN.ADD_NEW_SENSOR,
      variant: 'contained',
      onClick: () => {
        setModalShow(true)
      },
    },
    {
      label: SENSOR_MANAGEMENT_SCREEN.DELETE_ALL_SENSOR,
      variant: 'outlined',
      onClick: () => deleteSensors(),
    }
  ]

  const createColumns = [
    {
      field: '_id',
      headerName: 'ID',
      align: 'center',
      flex: 1,
      headerAlign: 'center',
    },
    {
      field: '_name',
      headerName: 'Name',
      type: 'text',
      align: 'left',
      flex: 1,
      headerAlign: 'left',
    },
    {
      field: '_sensorType',
      headerName: 'Sensor Type',
      type: 'text',
      align: 'left',
      flex: 1,
      headerAlign: 'left',
    },
    {
      field: '_sourceId',
      headerName: 'Source ID',
      type: 'text',
      align: 'left',
      flex: 1,
      headerAlign: 'left',
    },
    {
      field: '_description',
      headerName: 'Description',
      type: 'text',
      align: 'left',
      flex: 1,
      headerAlign: 'left',
    },
    {
      field: '_modelLocationElementId',
      headerName: 'Model Package ID',
      type: 'text',
      align: 'center',
      flex: 1,
      headerAlign: 'center',
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      flex: 1,
      cellClassName: 'actions',
      align: 'center',
      sortable: false,
      headerAlign: 'center',
      getActions: ({ id, row }) => [
        <GridActionsCellItem
          key={`view-${id}`}
          icon={<VisibilityIcon color="primary" />}
          label="View"
          onClick={() => showItems(row)}
          color="inherit"
        />,
        <GridActionsCellItem
          key={`Edit-${id}`}
          icon={<EditIcon color="primary" />}
          label="Edit"
          onClick={() => editTelItem(row)}
          color="inherit"
        />,
        <GridActionsCellItem
          key={`delete-${id}`}
          icon={<Delete color="primary" />}
          label="Delete"
          onClick={() => deleteSelectedTel(row)}
          color="inherit"
        />
      ],
    }
  ];

  const submitSensorDetails = async (formData) => {
    if (isEditMode) {
      const dataToSend = {
        _name: formData.name,
        _description: formData.description,
        _sensorType: formData.sensorType,
        _sourceId: formData.sourceId,
        _modelLocationElementId: formData.modelLocationElementId,
      };
      try {
        await IafItemSvc.updateRelatedItem(
          sensorsCollection._id,
          currentItemToEdit._id,
          dataToSend
        );
        setToasterType(`${TOASTER_MESSAGE.SUCCESS_BTN}`)
        setToasterMessage(`${TOASTER_MESSAGE.SENSOR_UPDATED_SUCCESSFULLY}`)
        setSuccessMessage(true);

      } catch (e) {
        console.log('Error: ', e);
        setToasterType(`${TOASTER_MESSAGE.ERROR_BTN}`)
        setToasterMessage(`${TOASTER_MESSAGE.ERROR_IN_UPDATING}`)
        setSuccessMessage(true);

      }
      finally {
        setTimeout(() => {
          setSuccessMessage(false);
        }, 3000);
        setModalShow(false);
        handleCloseModal();

        getTelItems();
      }

    }
    else {
      try {
        await IafItemSvc.createRelatedItems(
          sensorDetails._id,
          [formData]
        );
        setToasterType(`${TOASTER_MESSAGE.SUCCESS_BTN}`)
        setToasterMessage(`${TOASTER_MESSAGE.SENSOR_CREATED_SUCCESSFULLY}`)
        setSuccessMessage(true);

      } catch (e) {
        console.log('Error: ', e);
        setToasterType(`${TOASTER_MESSAGE.ERROR_BTN}`)
        setToasterMessage(`${TOASTER_MESSAGE.ERROR_IN_CREATING}`)
        setSuccessMessage(true);
      }

      finally {
        setTimeout(() => {
          setSuccessMessage(false);
        }, 3000);
        setModalShow(false);
        handleCloseModal();
        getTelItems();
      }
    }
  }

  const deleteSelectedTel = (item) => {
    const itemId = item._id
    setTelToDelete(itemId);
    setIsConfirmationModalOpen(true);
  };

  const handleDeleteConfirmed = async (itemId) => {
    if (delAllSensors) {
      try {
        await IafItemSvc.deleteRelatedItemsBulk(sensorDetails._id, { query: {} },)
        setToasterType(`${TOASTER_MESSAGE.SUCCESS_BTN}`)
        setToasterMessage(`${TOASTER_MESSAGE.DELETE_ALL_CREATED_SUCCESSFULLY}`)
        setSuccessMessage(true);
      } catch (e) {
        console.log('Error: ', e);
        setToasterType(`${TOASTER_MESSAGE.ERROR_BTN}`)
        setToasterMessage(`${TOASTER_MESSAGE.ERROR_IN_DELETING}`)
        setSuccessMessage(true);
      }
      finally {
        setTimeout(() => {
          setSuccessMessage(false);
        }, 3000);
        handleDeleteCancelled();
        getTelItems();
      }
    }
    else {
      await IafItemSvc.deleteRelatedItem(sensorDetails._id, itemId);
      getTelItems();
      setToasterType(`${TOASTER_MESSAGE.SUCCESS_BTN}`)
      setToasterMessage(`${TOASTER_MESSAGE.DELETE_SUCCESSFULLY}`)
      setSuccessMessage(true);
      setTimeout(() => {
        setSuccessMessage(false);
      }, 3000);
      handleDeleteCancelled()
    }
  };

  const handleDeleteCancelled = () => {
    setIsConfirmationModalOpen(false);
    setTelToDelete(null);
    setDelAllSensors(false)
  };

  const editTelItem = async (telItem) => {
    setIsEditMode(true);
    setCurrentItemToEdit(telItem);
    setModalShow(true);
  };

  const showItems = (item) => {
    dispatch(setSensorDetails(item))
    localStorage.setItem('sensorDetails',JSON.stringify(item))
    history.push(`/sensorDetails`)
  }

  const deleteSensors = async () => {
    setIsConfirmationModalOpen(true)
    setDelAllSensors(true)
  }

  return (
    <Container maxWidth="false">
      <PageHeader showBackButton={true} pageText={SENSOR_MANAGEMENT_SCREEN.SENSOR_LIST} />
      {successMessage && <ToastComponent open={successMessage} severity={toasterType} message={toasterMessage} handleClose={() => { setSuccessMessage(false) }} />}
      <MUITable
        title={sensorDetails._name}
        rows={telItemsList}
        columns={createColumns}
        getRowId={(row) => row._id}
        isLoading={isLoading}
        buttons={buttonToAdd}
        showSearchBar={false}
      />

      <Modal show={modalShow} >
        <Modal.Header>
          <Modal.Title>
            {
              isEditMode ? SENSOR_MANAGEMENT_SCREEN.EDIT_SENSOR_ITEM : SENSOR_MANAGEMENT_SCREEN.ADD_NEW_SENSOR
            }
          </Modal.Title>
          <div onClick={() => handleCloseModal()} className="close-icon"><CloseIcon /></div>
        </Modal.Header>
        <Modal.Body>
          {!isEditMode ? (
            <div className="style_form_with_updated">
              <form onSubmit={handleSubmit(submitSensorDetails)}>
                <div className='form-group'>
                  <label>{SENSOR_MANAGEMENT_SCREEN.SENSOR_NAME}</label>
                  <input type='text' className='form-control'  {...register("name", { required: true })} />
                  {errors.name && <div className="text-danger mt-1">{SENSOR_MANAGEMENT_SCREEN.SENSOR_NAME_IS_REQUIRED}</div>}
                </div>
                <div className='form-group'>
                  <label>{SENSOR_MANAGEMENT_SCREEN.SENSOR_TYPE} </label>
                  <input type='text' className='form-control'  {...register("sensorType", { required: true })} />
                  {errors.sensorType && <div className="text-danger mt-1">{SENSOR_MANAGEMENT_SCREEN.SENSOR_TYPE_IS_REQUIRED}</div>}
                </div>
                <div className='form-group'>
                  <label>{SENSOR_MANAGEMENT_SCREEN.SENSOR_DESCRIPTION}</label>
                  <input type='text' className='form-control'  {...register("description", { required: true })} />
                  {errors.description && <div className="text-danger mt-1">{SENSOR_MANAGEMENT_SCREEN.SENSOR_DESCRIPTION_IS_REQUIRED}</div>}
                </div>
                <div className='form-group'>
                  <label>{SENSOR_MANAGEMENT_SCREEN.SOURCE_ID} </label>
                  <input type='text' className='form-control'  {...register("sourceId", { required: true })} />
                  {errors.unit && <div className="text-danger mt-1">{SENSOR_MANAGEMENT_SCREEN.SOURCE_ID}</div>}
                </div>
                <div className='form-group'>
                  <label>{SENSOR_MANAGEMENT_SCREEN.MODEL_LOCATION_ID}</label>
                  <input type='text' className='form-control'  {...register("modelLocationElementId", { required: false })} />
                </div>
                <div className="modal-button mt-4">
                  <Button className="secondary_button submit-button" type="button" btnLabel={MODEL.CANCEL} onClick={() => {
                    handleCloseModal()
                  }} />
                  {!isEditMode ? (
                    <Button className="primary_button submit-button ml-2" type="submit" btnLabel={MODEL.CREATE} />
                  ) : (
                    <Button className="primary_button submit-button ml-2" type="submit" btnLabel={MODEL.SAVE_BUTTON} />
                  )}

                </div>
              </form>
            </div>
          ) : (
            <div className="style_form_with_updated">
              <form onSubmit={handleSubmit(submitSensorDetails)}>
                <div className='form-group'>
                  <label>{SENSOR_MANAGEMENT_SCREEN.SENSOR_NAME}</label>
                  <input type='text' defaultValue={currentItemToEdit._name} className='form-control'  {...register("name", { required: true })} />
                  {errors.name && <div className="text-danger mt-1">{SENSOR_MANAGEMENT_SCREEN.SENSOR_NAME_IS_REQUIRED}</div>}
                </div>
                <div className='form-group'>
                  <label>{SENSOR_MANAGEMENT_SCREEN.SENSOR_TYPE} </label>
                  <input type='text' defaultValue={currentItemToEdit._sensorType} className='form-control'  {...register("sensorType", { required: true })} />
                  {errors.sensorType && <div className="text-danger mt-1">{SENSOR_MANAGEMENT_SCREEN.SENSOR_TYPE_IS_REQUIRED}</div>}
                </div>
                <div className='form-group'>
                  <label>{SENSOR_MANAGEMENT_SCREEN.SENSOR_DESCRIPTION}</label>
                  <input type='text' defaultValue={currentItemToEdit._description} className='form-control'  {...register("description", { required: true })} />
                  {errors.description && <div className="text-danger mt-1">{SENSOR_MANAGEMENT_SCREEN.SENSOR_DESCRIPTION_IS_REQUIRED}</div>}
                </div>
                <div className='form-group'>
                  <label>{SENSOR_MANAGEMENT_SCREEN.SOURCE_ID} </label>
                  <input type='text' defaultValue={currentItemToEdit._sourceId} className='form-control'  {...register("sourceId", { required: true })} />
                  {errors.unit && <div className="text-danger mt-1">{SENSOR_MANAGEMENT_SCREEN.SOURCE_ID}</div>}
                </div>
                <div className='form-group'>
                  <label>{SENSOR_MANAGEMENT_SCREEN.MODEL_LOCATION_ID}</label>
                  <input type='text' defaultValue={currentItemToEdit._modelLocationElementId} className='form-control'  {...register("modelLocationElementId", { required: false })} />
                </div>
                <div className="modal-button mt-4">
                  <Button className="secondary_button submit-button" type="button" btnLabel={MODEL.CANCEL} onClick={() => { handleCloseModal() }} />
                  {!isEditMode ? (
                    <Button className="primary_button submit-button ml-2" type="submit" btnLabel={MODEL.CREATE} />
                  ) : (
                    <Button className="primary_button submit-button ml-2" type="submit" btnLabel={MODEL.SAVE_BUTTON} />
                  )}
                </div>
              </form>
            </div>)}
        </Modal.Body>
      </Modal>
      {isConfirmationModalOpen && (
        <CommonPopup
          isOpen={isConfirmationModalOpen}
          title={COMMON_POPUP_TEXT.TITLE_DELETE}
          type={COMMON_POPUP_TEXT.TYPE_DELETE}
          message={COMMON_POPUP_TEXT.MESSAGE_DELETE}
          onConfirm={() => handleDeleteConfirmed(telToDelete)}
          onCancel={handleDeleteCancelled}
          showConfirmButton={true}
        />
      )}
    </Container>
  )
}

TelemetryCollection.propTypes = {
  selectedItems: object,
  sensorDetails: object,
  props: object
}