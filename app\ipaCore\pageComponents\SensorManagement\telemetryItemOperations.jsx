import { IafItemSvc } from '@dtplatform/platform-api';
import React, { memo, useEffect, useState } from 'react';
import GraphService from './GraphService';
import moment from 'moment';
import '@Scss/viewer.scss';
import Button from '../utils/Button';
import ToastComponent from '../utils/ToastComponent';
import ContentCard from '../utils/contentcard/ContentCard';
import ContentCardBody from '../utils/contentcard/ContentCardBody';
import ContentCardHeader from '../utils/contentcard/ContentCardHeader';
import PropTypes from 'prop-types';
import Loader from '../utils/Loader';
import { TOASTER_MESSAGE, TELEMETRY_ITEM_OPERATION } from '../utils/constants'
import { Paper, Typography, Grid, Container } from '@mui/material';
import TimestampFormatter from '../utils/TimestampFormatter';
import { useSelector } from 'react-redux';
import PageHeader from "../Helpers/PageHeader";
import _ from 'lodash';

const TelemetryItemOperations = (props) => {
    const { selectedItems } = props;
    const [sensorReadings, setSensorReadings] = useState([]);
    const [isReadingCreationInProcess, setIsReadingCreationInProcess] = useState(false);
    const [successMessage, setSuccessMessage] = useState(false);
    const [toasterType, setToasterType] = useState("")
    const [toasterMessage, setToasterMessage] = useState("");
    const [showSpinnerStatus, setShowSpinnerStatus] = useState(true);
    const eleParametersToShowOnReading = ['_ts', '_tsMetadata', '_sourceId']
    const sensorData = useSelector((state) => state.sharedDataSensorSlice.sensorDetails) ?? JSON.parse(localStorage.getItem('sensorDetails'));
    const sensorCollectionData = useSelector((state) => state.sharedDataSensorSlice.sensorList) ?? JSON.parse(localStorage.getItem('sensorList'));
    const projectInfo = props.selectedItems.selectedProject

    useEffect(() => {
        if (!_.isEmpty(sensorData)) {
            getReadings();
        } else {
            setShowSpinnerStatus(true)
        }
    }, [])


    let ctx = { _namespaces: projectInfo._namespaces };

    const getReadings = async () => {
        const dataQuery = { query: { "_tsMetadata._telItemId": sensorData._id } };
        let options = { page: { _pageSize: 50, _offset: 0 }, sort: { "_ts": -1 } };
        const relatedReadings = await IafItemSvc.getRelatedReadingItems(sensorCollectionData._userItemId, dataQuery, ctx, options);
        setSensorReadings(relatedReadings._list);
        setShowSpinnerStatus(false)
    }

    const riskAnalysis = async (reading, thresHold) => {
        return {
            status: reading > parseFloat(thresHold) ? "danger" : (reading < parseFloat(thresHold) && ((parseFloat(thresHold) - reading) <= 3)) ? 'immediate risk' : 'normal',
            severity: (reading < parseFloat(thresHold) && ((parseFloat(thresHold) - reading) <= 3) ? "highly severe" : (reading > parseFloat(thresHold)) ? "danger state" : "normal state"),
        }
    }

    const createReadingOfSensor = async () => {
        try {
            setIsReadingCreationInProcess(true);
            const readingVal = Math.floor((Math.random() * 30 + 220) * 10) / 100;
            const risks = await riskAnalysis(readingVal, sensorData._thresholdValue);
            const readingInfo = [
                {
                    reading: readingVal,
                    _ts: moment().format(),
                    status: risks.status,
                    severity: risks.severity,
                    sensorCollectionId: sensorCollectionData._id,
                    sensorName: sensorData._name,
                    sensorLocation: sensorData._location,
                    _tsMetadata: {
                        _telItemId: sensorData._id,
                        _sourceId: sensorData._sourceId
                    }
                }
            ]

            const createdReading = await IafItemSvc.createRelatedReadingItems(sensorCollectionData._id, readingInfo, ctx);
            getReadings();
            if (createdReading) {
                await IafItemSvc.updateRelatedItem(sensorCollectionData._id, sensorData._id,
                    { ...sensorData, status: readingInfo[0].status, severity: readingInfo[0].severity }, ctx);
                setIsReadingCreationInProcess(false);
                setSuccessMessage(true);
                setToasterType(`${TOASTER_MESSAGE.SUCCESS_BTN}`);
                setToasterMessage(`${TOASTER_MESSAGE.READING_CREATED_SUCCESSFULLY}`);
                let timer = setTimeout(() => {
                    setSuccessMessage(false);
                    clearTimeout(timer);
                }, 2000);
            } else {
                setIsReadingCreationInProcess(false);
            }
        } catch (e) {
            setIsReadingCreationInProcess(false);
            console.log('err', e);
        }
    }

    return (
        <Container maxWidth="false">
            <PageHeader showBackButton={true} pageText={sensorData._name} />
            <ContentCard>
                <ContentCardHeader classNames='' withdivider={true}>
                    <div className='card-header-content d-flex flex-wrap align-items-center'>
                        <div className='col-md-4 px-0'>


                        </div>
                        <div className='col-md-4 text-center'>
                            {successMessage && <ToastComponent open={successMessage} severity={toasterType} message={toasterMessage} handleClose={() => { setSuccessMessage(false) }} />}
                            {isReadingCreationInProcess && <h6 className='name-color text-center txt-info font-bold'>{TELEMETRY_ITEM_OPERATION.FETCHING_READING}</h6>}
                        </div>
                        <div className='col-md-4 px-0'>
                            <Button className="primary_button d-block ml-auto" btnLabel={TELEMETRY_ITEM_OPERATION.FETCH_NEW_READING} onClick={createReadingOfSensor} />
                        </div>
                    </div>
                </ContentCardHeader>
                <ContentCardBody classNames='secmin-height'>
                    <div className='row'>
                        {!showSpinnerStatus ?
                            <>
                                <div className='col-md-8 pl-0 pr-5 border-right py-5'>
                                    <div className='graphsec'>
                                        {(sensorReadings && sensorReadings.length > 0) &&
                                            <GraphService allReadings={sensorReadings}></GraphService>
                                        }
                                    </div>
                                </div>
                                <div className='col-md-4 pl-4 pr-0'>
                                    <div className='readinglist-sec'>
                                        <h3 className="subtitle-heading p-1">{TELEMETRY_ITEM_OPERATION.READING_HEADING}</h3>
                                        {sensorReadings && sensorReadings.length > 0 ? (<ul className='list-group'>
                                            {sensorReadings.map((readingEle) => (
                                                <Paper
                                                    className="sensorReadinglist"
                                                    key={readingEle?._id}
                                                >
                                                    <Typography variant="subtitle2" color="textSecondary">
                                                        <TimestampFormatter timestamp={readingEle?._ts} />
                                                    </Typography>
                                                    <Grid container spacing={2} sx={{ marginTop: 1 }}>
                                                        {Object.keys(readingEle)
                                                            .filter(
                                                                (key) => key !== '_id' && !eleParametersToShowOnReading.includes(key) && typeof readingEle[key] !== 'object'
                                                            )
                                                            .map((key) => (
                                                                <Grid item xs={6} key={key}>
                                                                    <Typography variant="body2" color="textSecondary" component="span">
                                                                        <strong>{key}:</strong> {readingEle[key]}
                                                                    </Typography>
                                                                </Grid>
                                                            ))}
                                                    </Grid>
                                                </Paper>
                                            ))}
                                        </ul>) : <h6 className='text-center py-4 txt-info font-bold'>{TELEMETRY_ITEM_OPERATION.NO_READING_FOUND}</h6>}
                                    </div>
                                </div>
                            </> : <Loader />
                        }

                    </div>
                </ContentCardBody>
            </ContentCard>
        </Container>
    )
}

TelemetryItemOperations.propTypes = {
    selectedItems: PropTypes.array
}

export default memo(TelemetryItemOperations);
