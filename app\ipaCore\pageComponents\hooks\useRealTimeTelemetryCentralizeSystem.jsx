//Hook to tranform and store the readings on store to show realttim data
import _ from "lodash";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchAllTelemetryDevices, pushNewNotificaition, pushNewReadingOnGraphCollection, setTelDevicesReadings } from "../../redux/MqttOpsSlice";
import { generateNotification } from "../utils/common-utils";

const useRealTimeTelemetryCentralizeSystem = ({ eventMessage }) => {
    const dispatch = useDispatch();
    const { telemetryDevicesWithLatestReading, machineNotification } = useSelector((state) => state.MQTTOpsSlice);
    const [currentReadings, setCurrentReadings] = useState(telemetryDevicesWithLatestReading);

    useEffect(() => {
        if (!telemetryDevicesWithLatestReading || _.isEmpty(telemetryDevicesWithLatestReading)) {
            dispatch(fetchAllTelemetryDevices())
        }
    }, [])

    useEffect(() => {
        if (telemetryDevicesWithLatestReading && eventMessage) {
            const normalizedData = normalizeReadingData(eventMessage.toString());
            //Info:now need to update the store
            if (normalizedData && !_.isEmpty(normalizedData)) {
                pushCurrentReadingOnRespectiveTelDevice(normalizedData)
            }
        }
    }, [eventMessage])

    const normalizeReadingData = (redaingData) => {
        const { readings } = JSON.parse(redaingData);
        let readingsWithRequiredInfo = []
        readings.forEach((r) => {
            var telItem = _.find(telemetryDevicesWithLatestReading, { _sourceId: r._sourceId });
            var readingsObj = { ...r, ...constructReadings(r._sourceId, r._ts, telItem?._id) };
            readingsWithRequiredInfo.push(readingsObj);
        })
        return readingsWithRequiredInfo;
    }

    const constructReadings = (sourceId, ts, telId) => {
        let tsDate = new Date(ts);
        let readingsObj = {
            "_ts": tsDate.toISOString(),
            "_tsMetadata": {
                "_telItemId": telId,
                "_sourceId": sourceId
            }
        };
        return readingsObj;
    }

    const throttledDispatch = _.throttle((notifications, dispatchFunction) => {
        dispatch(dispatchFunction(notifications));
    }, 50);

    const pushCurrentReadingOnRespectiveTelDevice = async (normalizedReadings) => {
        //Info: Push Notification
        let newNotificationsToPush = generateNotification(normalizedReadings, machineNotification);
        if (!_.isEmpty(newNotificationsToPush)) {
            // throttledDispatch(newNotificationsToPush, pushNewNotificaition);
            dispatch(pushNewNotificaition(newNotificationsToPush));
        }
        let finalObjToPush = telemetryDevicesWithLatestReading.map((telItem) => {
            let r = normalizedReadings.find(rItem => rItem._sourceId === telItem._sourceId);
            // Info: when need to add/show only current reading
            const readingsToAdd = r ? r : telItem.readings || null;
            r && dispatch(pushNewReadingOnGraphCollection(r));
            return { ...telItem, readings: readingsToAdd };
        })
        throttledDispatch(finalObjToPush, setTelDevicesReadings);
        setCurrentReadings(finalObjToPush);
        return finalObjToPush;
    }
    return {
        currentReadings
    }
}


export default useRealTimeTelemetryCentralizeSystem;


