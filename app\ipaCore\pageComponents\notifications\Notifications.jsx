import React from 'react';
import ToastComponent from '../utils/ToastComponent';
import { useSelector } from 'react-redux';
import { useState, useRef } from 'react';
import useConnectMQTTAndRetriveLatestData from '../hooks/useActiveMqConfiguration';
import _ from 'lodash';
import { useEffect } from 'react';
import DraggableContent from '../Dashboard/DraggableContent';
import DraggablePanel from '../panels/DraggablePanel';
import { sourceNames, TOASTER_MESSAGE } from '../utils/constants';
import { MODEL_MACHINE_STATUS, NOTIFICATION } from '../utils/constants';


const Notifications = () => {
    const prevValue = useRef(null);
    const { currentReadings } = useConnectMQTTAndRetriveLatestData();


    const { machineGraphs, machineNotification, telemetryDevicesWithLatestReading } = useSelector(state => state.MQTTOpsSlice)


    const [isDraggablePanelOpen, setIsDraggablePanelOpen] = useState(false);
    const [sourceId, setSourceId] = useState(null);
    const [anamolyResolved, setAnamolyResolved] = useState(false);


    const filteredMachine = machineGraphs.filter(item => item._sourceId === sourceId);


    let allFilteredData = filteredMachine.length && filteredMachine[0].graphs.map((item) => {
        return { data: item['graphYAxis'].filter((item) => typeof item === "number"), label: item['graphParameter'] }
    })

    let finalDataForGraphs = allFilteredData && allFilteredData.filter((item) => item.label === `${NOTIFICATION.SPINDLE}`)


    useEffect(() => {
        let anamolyId
        const anomalyCheckMachine = machineNotification.filter(item => item._anomaly === `${MODEL_MACHINE_STATUS.hasAnomaly}`);
        if (anomalyCheckMachine.length) {
            setIsDraggablePanelOpen(true)
            setSourceId(anomalyCheckMachine[0]?._sourceId)
            prevValue.current = true;
            setAnamolyResolved(false)
        } else {
            if (prevValue.current) {
                // Only set resolved if the dialog was previously open
                setAnamolyResolved(true);
                prevValue.current = false; // Update the previous value
            }
            setIsDraggablePanelOpen(false)

            // for resolving the anamoly message
            anamolyId = setTimeout(() => {
                setAnamolyResolved(false)
            }, NOTIFICATION.TOASTER_DELAY);
        }

        return () => clearTimeout(anamolyId)

    }, [machineNotification])


    const currentSelectedMachine = telemetryDevicesWithLatestReading.find(item => item._sourceId === sourceId);

    const stoppedMachine = machineNotification.filter(item => item._status === `${MODEL_MACHINE_STATUS.stopped}`);
    const stoppedMachineNames = stoppedMachine.map(item => sourceNames[item._sourceId]).join(', ') + " " + `${NOTIFICATION.HAS_BEEN_STOP}`;

    return (
        <>
            {!_.isEmpty(stoppedMachineNames) && <ToastComponent open={stoppedMachine.length} severity={TOASTER_MESSAGE.ERROR_BTN} message={stoppedMachineNames} handleClose={() => { }} className='snackbar-mt' anchorOrigin={{ vertical: 'top', horizontal: 'right' }} />}
            {anamolyResolved && <ToastComponent open={anamolyResolved} severity={TOASTER_MESSAGE.SUCCESS_BTN} message={NOTIFICATION.ANAMOLY_RESOLVED} handleClose={() => { }} className='snackbar-mt' autoHide={true} anchorOrigin={{ vertical: 'top', horizontal: 'right' }} />}
            {!_.isEmpty(sourceId) && !_.isEmpty(finalDataForGraphs[0]) && <div className='drag-panel'><DraggablePanel
                isPanelOpen={isDraggablePanelOpen}
                position={{ left: 60, top: 40 }}
                handlePanelClose={() => setIsDraggablePanelOpen(false)}>
                <DraggableContent data={currentSelectedMachine} filteredMachine={filteredMachine} allFilteredData={finalDataForGraphs[0]} isPanelOpen={isDraggablePanelOpen} />
            </DraggablePanel></div>}
        </>
    )

}

export default Notifications;

