import React, { useEffect, useState } from 'react';
import TableHeader from './TableHeader';
import TableRow from './TableRow';
import '../../sass/common.scss';

const TableGrid = (props) => {
    const {
        headers,
        data,
        searchValue,
        showItems,
        editItem,
        deleteItem,
        isPagination, loading, toEdit, toDelete, toView, userItems, userView, getAllNamedUserItemList, projectProps, prop, reload, completionMessage, showRunOrch, showScheOrch, showDeleteOrch, toRunOrch, toScheduleOrch, toDeleteOrch
    } = props;

    const [rowData, setRowData] = useState(data);
    const [order, setOrder] = useState("ASC");

    useEffect(() => {
        setRowData(data)
    }, [data])

    const sorting = (col) => {
        if (order === "ASC") {
            const sorted = [...rowData].sort((a, b) =>
                a[col].toLowerCase() > b[col].toLowerCase() ? 1 : -1
            )
            setRowData(sorted);
            setOrder("DSC")
        }
        if (order === "DSC") {
            const sorted = [...rowData].sort((a, b) =>
                a[col].toLowerCase() < b[col].toLowerCase() ? 1 : -1
            )
            setRowData(sorted);
            setOrder("ASC")
        }
    }

    return (
        <>
            <table className="table table-striped style-table">
                <TableHeader headers={headers} clickHandler={sorting} />
                <TableRow
                    // @ts-ignore
                    data={rowData}
                    searchValue={searchValue}
                    showItems={showItems}
                    editItem={editItem}
                    deleteItem={deleteItem}
                    headers={headers}
                    isPagination={isPagination}
                    loading={loading}
                    toEdit={toEdit}
                    toView={toView}
                    toDelete={toDelete}
                    userView={userView}
                    userItems={userItems}
                    getAllNamedUserItemList={getAllNamedUserItemList}
                    prop={prop}
                    reload={reload}
                    completionMessage={completionMessage}
                    projectProps={projectProps}
                    showunOrch={showRunOrch}
                    showRScheOrch={showScheOrch}
                    showDeleteOrch={showDeleteOrch}
                    toRunOrch={toRunOrch}
                    toScheduleOrch={toScheduleOrch}
                    toDeleteOrch={toDeleteOrch}
                />

            </table>
        </>
    )
}

export default TableGrid;