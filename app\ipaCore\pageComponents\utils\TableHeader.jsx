import React from "react";
import { CommonButton } from './CommonButton';
import '@Scss/viewer.scss';

const TableHeader = ({ headers, clickHandler }) => {
    return (
        <thead>
            <tr>
                {
                    headers.map(({ key, value, sort }) => {
                        return (
                            <th style={{
                                width: (value == "_id" ? "100px" : "")
                            }}
                                key={value} > {key}
                                {sort ?
                                    <CommonButton
                                        iconClass="icofont-sort"
                                        btnClass="btn sortIcon-clr"
                                        clickHandler={() => clickHandler(value)}
                                    />
                                    : null
                                }
                            </th >
                        )
                    })
                }

            </tr>
        </thead >
    )
}



export default TableHeader;
