import React, { useEffect, useState } from "react";
import { filterSearch } from '../utils/CommonFunction';
import { Pagination } from '@mui/material';
import { <PERSON><PERSON>, Tooltip } from "@material-ui/core";
import { Modal, Form, Col } from "react-bootstrap";
import { IafFileSvc, IafItemSvc } from "@invicara/platform-api";
import DescriptionTwoToneIcon from '@mui/icons-material/DescriptionTwoTone';
import * as FileSaver from "file-saver";
import '@Scss/viewer.scss';
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import PeopleAltIcon from '@mui/icons-material/PeopleAlt';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import CreateIcon from '@mui/icons-material/Create';
import EventRepeatIcon from '@mui/icons-material/EventRepeat';
import InputIcon from '@mui/icons-material/Input';
import Loader from "./Loader";
import { COMMON_TABLE } from '../utils/constants';

export default function TableRow({ data,
    searchValue,
    showItems,
    editItem,
    deleteItem,
    headers,
    loading,
    toView,
    toDelete,
    toEdit,
    toInvite,
    userView,
    userItems,
    userInvite,
    isPagination,
    getAllNamedUserItemList,
    projectProps, prop, reload,
    completionMessage,
    showRunOrch, showScheOrch, showDeleteOrch, toRunOrch, toScheduleOrch, toDeleteOrch
}) {
    const [count, setCount] = useState(0)
    const [startPageIndex, setStatrPageIndex] = useState(0);
    const [endPageIndex, setEndPageIndex] = useState(perPageRecords - 1);
    const [currentPageToShow, setPageNumber] = useState(1);
    const [isDataSpicingInProcess, setDataSpilicingStatus] = useState(false);
    const [dataToShowOnPageSelect, setPageSelectData] = useState([]);
    const [perPageRecords, setPerPageRecords] = useState(5)
    const [totalPages, setTotalPages] = useState(0);
    const [spinner, setSpinner] = useState(false);
    const [modalShow, setModalShow] = useState(false);
    const [member, setMember] = useState("")
    const [issueDetail, setIssueDetails] = useState([])
    const [isDownloading, setDownloading] = useState(false);

    useEffect(() => {
        const length = headers.length;
        setCount(length);
    }, [])

    useEffect(() => {
        if (loading === false) {
            if (isPagination) {
                if (searchValue) {
                    setTotalPages(1)
                } else {
                    setTotalPages(Math.ceil(totalPageRecords / perPageRecords))
                }
                getIndexsToChangePage(currentPageToShow);
                setDataSpilicingStatus(true);
            } else {
                setPageSelectData(data);
            }
        }
    },
        [perPageRecords, loading, data, searchValue]
    )

    useEffect(() => {
        if (isDataSpicingInProcess) {
            spliceDataToShow(data, startPageIndex, endPageIndex);
        }
    }, [isDataSpicingInProcess, perPageRecords, data])

    const totalPageRecords = parseInt(data.length);

    useEffect(() => {
        if (isPagination) {
            getIndexsToChangePage(currentPageToShow);
        }
    }, [currentPageToShow, perPageRecords]);

    const selectFunction = (e) => {
        setPerPageRecords(parseInt(e.target.value))
        displayPage(1)
    }

    const displayPage = (e) => {
        setPageNumber(parseInt(e));
    }

    const getIndexsToChangePage = (pageNo) => {
        setDataSpilicingStatus(true);
        let end_page_index = perPageRecords * pageNo - 1;
        let start_page_index = perPageRecords * pageNo - perPageRecords;
        setStatrPageIndex(start_page_index);
        if (end_page_index > totalPageRecords) {
            setEndPageIndex(totalPageRecords - 1);
        } else {
            setEndPageIndex(end_page_index);
        }
        setDataSpilicingStatus(true);
    };

    const spliceDataToShow = (data, startIndex, endIndex) => {
        const splicedData = data.slice(parseInt(startIndex), parseInt(endIndex + 1));
        setPageSelectData(splicedData);
        setDataSpilicingStatus(false);
    }

    const handleClose = () => {
        setModalShow(false);
        setIssueDetails([]);
        setMember("");
    }

    const handleAssignTaskClick = (item) => {
        setModalShow(true)
        setIssueDetails(item)
        if (item.assignedTo.assignedPerson == "NA") {
            setMember("")
        }
        else {
            setMember(item.assignedTo.assignedPerson._id)
        }
    }

    const handleSelectChange = (e) => {
        setMember(e.target.value)
    }

    const submitAssignTask = async () => {
        if (!member) {
            console.error("please select a member before assigning the task.")
            return;
        }
        setSpinner(true)
        let newData = [issueDetail];
        const assignedMember = userItems.filter((user) => user._id === member);
        newData.forEach((item) => {
            item.assignedTo = {
                assignedAt: issueDetail._location,
                assignedPerson: { _id: assignedMember[0]._id, _firstname: assignedMember[0]._firstname, _lastname: assignedMember[0]._lastname },
                assignee: { _id: projectProps.user._id, _firsName: projectProps.user._firstname, _lastName: projectProps.user._lastname },
            }
            item._reportStatus = "inProgress",
                item.assignedPersonUserId = assignedMember[0]._id
        })
        try {
            const list = await IafItemSvc.getNamedUserItems({ query: { _itemClass: "NamedUserCollection", _userType: "issues_collection" } });
            try {
                const updateIssue = await IafItemSvc.updateRelatedItem(list._list[0]._id, issueDetail._id, newData[0]);
                setSpinner(false)
                getAllNamedUserItemList()
                handleClose();
            } catch (e) {
                console.log("Error", e)
            }
        } catch (e) {
            console.log("Error", e)
        }
    }

    const handleCloseTask = async (item) => {
        setSpinner(true)
        let newData = [item];
        newData.forEach((item) => {
            item._closedBy = {
                _userId: projectProps.user._id,
                _userName: projectProps.user._fullname,
                _closedTime: new Date().toJSON()
            }
            item._reportStatus = "closed"
            item._endTime = new Date().toJSON()
        })
        try {
            const list = await IafItemSvc.getNamedUserItems({ query: { _itemClass: "NamedUserCollection", _userType: "issues_collection" } });
            try {
                const closeIssue = await IafItemSvc.updateRelatedItem(list._list[0]._id, item._id, newData[0]);
                setSpinner(false)
                getAllNamedUserItemList();
            } catch (e) {
                console.log("Error", e)
            }

        } catch (e) {
            console.log("Error", e)
        }
    }

    const handleReportDownload = async (item) => {
        if (!isDownloading) {
            setDownloading(true);
            let fileId = item._report._fileId;
            const fileUrl = await IafFileSvc.getFileUrl(fileId);
            try {
                if (fileUrl._name.split('.').pop() === "xlsx") {
                    window.open(fileUrl._url, " ")
                    FileSaver.saveAs(fileUrl._url, fileUrl._name);
                }
                else {
                    FileSaver.saveAs(fileUrl._url, fileUrl._name + '.xlsx');
                }
            }
            catch (error) {
                console.log("Error", error)
            }
            finally {
                setTimeout(() => {
                    setDownloading(false);
                }, 1000);
            }
        }
    };

    const handleOnWithdrawIssue = async (item) => {
        const list = await IafItemSvc.getNamedUserItems({ query: { _itemClass: "NamedUserCollection", _userType: "issues_collection" } });
        const updateIssue = await IafItemSvc.updateRelatedItem(list._list[0]._id, item._id, { ...item, _reportStatus: "withdrawn" });
        completionMessage()
        reload()
    }

    return (
        <>
            {!loading ? (
                <>
                    {!isDataSpicingInProcess &&
                        <tbody>
                            {
                                (dataToShowOnPageSelect && dataToShowOnPageSelect.length > 0)
                                    ? (
                                        <>
                                            {filterSearch(dataToShowOnPageSelect, searchValue, data)
                                                .map((item, i) => (
                                                    <tr className="text-wrap" key={i} >
                                                        {
                                                            headers.map((column, index) => {
                                                                if (column.value === "_id" || column.value === "id") {
                                                                    return (
                                                                        <td key={index}>
                                                                            {`${perPageRecords * (currentPageToShow - 1) + i + 1}`}
                                                                        </td>
                                                                    )
                                                                }
                                                                else if (column.value !== "actions" && column.value !== "_eventLocation"
                                                                    && column.value !== "userGroups" && column.value !== "geometry"
                                                                    && column.value !== "_assignTask" && column.value !== "_fileDetails"
                                                                    && column.value !== "assignedTo" && column.value !== "_location" && column.value !== "_closeTask" && column.value !== "_closedBy" && column.value !== "_withdraw") {
                                                                    return <td key={index}>
                                                                        {item[column.value]}
                                                                    </td>
                                                                }
                                                                else if (column.value === "_eventLocation" || column.value == "geometry" || column.value == "_location") {
                                                                    return <td key={index}>{item[column.value]?.name}</td>
                                                                }
                                                                else if (column.value === "userGroups") {
                                                                    return <td key={index}>  {
                                                                        item[column.value].map((group) => {
                                                                            return (
                                                                                <>{group._name} &nbsp;</>
                                                                            )
                                                                        })
                                                                    }</td>
                                                                }
                                                                else if (column.value === "assignedTo") {
                                                                    return (
                                                                        <>
                                                                            {item.assignedTo.assignedPerson == "NA" ? (<>
                                                                                <td key={index}>
                                                                                    {COMMON_TABLE.NA}
                                                                                </td>
                                                                            </>) : (<>
                                                                                <td key={index}>
                                                                                    {item[column.value].assignedPerson._firstname}

                                                                                </td>
                                                                            </>)}
                                                                        </>
                                                                    )
                                                                }

                                                                else if (column.value === "_closedBy") {
                                                                    return (
                                                                        <>
                                                                            {item._userName == "NA" ? (<>
                                                                                <td key={index}>
                                                                                    {COMMON_TABLE.NA}
                                                                                </td>
                                                                            </>) : (<>
                                                                                <td key={index}>
                                                                                    {item[column.value]._userName}
                                                                                </td>
                                                                            </>)}
                                                                        </>
                                                                    )
                                                                }
                                                                else if (column.value === "_assignTask") {
                                                                    return (
                                                                        <>
                                                                            {item._reportStatus !== "closed" ? (<>
                                                                                {item.assignedTo.assignedPerson == "NA" ? (<> <td key={index} onClick={() => { handleAssignTaskClick(item) }}>
                                                                                    <Button variant="text" color="primary" >
                                                                                        {COMMON_TABLE.ASSIGN}
                                                                                    </Button>
                                                                                </td></>) : (<> <td key={index} onClick={() => { handleAssignTaskClick(item) }}>
                                                                                    <Button variant="text" color="primary" >
                                                                                        {COMMON_TABLE.CHANGE_MEMBER_BTN}
                                                                                    </Button>
                                                                                </td></>)}
                                                                            </>) : (<td key={index}>{COMMON_TABLE.NA}</td>)}
                                                                        </>
                                                                    )
                                                                }
                                                                else if (column.value === "_closeTask") {
                                                                    return (<>
                                                                        {item._reportStatus !== "closed" ? (<><td key={index} onClick={() => { handleCloseTask(item) }}>
                                                                            <Button variant="text" color="primary" >
                                                                                <span>
                                                                                    <span>{COMMON_TABLE.CLOSE}</span>
                                                                                </span>
                                                                            </Button>
                                                                        </td></>) : (<><td key={index} >
                                                                            <Button variant="text" color="primary" disabled >
                                                                                {COMMON_TABLE.CLOSED}
                                                                            </Button>
                                                                        </td></>)}
                                                                    </>
                                                                    )
                                                                }

                                                                else if (column.value === "_withdraw") {
                                                                    return (
                                                                        <>
                                                                            {(item._reportStatus == "open") ? (<> <td key={index} onClick={() => { handleOnWithdrawIssue(item) }}>
                                                                                <Button variant="text" color="primary" >
                                                                                    {COMMON_TABLE.WITHDRAW}
                                                                                </Button>
                                                                            </td></>) : (<> <td key={index}>
                                                                                <Button variant="text" color="primary" disabled>
                                                                                    {COMMON_TABLE.WITHDRAW}
                                                                                </Button>
                                                                            </td></>)
                                                                            }
                                                                        </>
                                                                    )
                                                                }

                                                                else if (column.value === "_fileDetails") {
                                                                    return (
                                                                        <td key={index}>
                                                                            <button className="btn">
                                                                                <DescriptionTwoToneIcon style={{ color: "#5046e5", fontSize: "20px" }} onClick={() => handleReportDownload(item)} />
                                                                            </button>
                                                                        </td>
                                                                    )
                                                                }

                                                                else if (column.value === "_withdraw") {
                                                                    return (
                                                                        <>
                                                                            {(item._reportStatus == "open") ? (<> <td key={index} onClick={() => { handleOnWithdrawIssue(item) }}>
                                                                                <Button variant="text" color="primary" >
                                                                                    {COMMON_TABLE.WITHDRAW}                                                                                    </Button>
                                                                            </td></>) : (<> <td key={index}>
                                                                                <Button variant="text" color="primary" disabled>
                                                                                    {COMMON_TABLE.WITHDRAW}
                                                                                </Button>
                                                                            </td></>)
                                                                            }
                                                                        </>)
                                                                }

                                                                else if (column.value === "_fileDetails") {
                                                                    return (
                                                                        <td key={index}>
                                                                            <button className="btn">
                                                                                <DescriptionTwoToneIcon style={{ color: "#5046e5", fontSize: "20px" }} onClick={() => handleReportDownload(item)} />
                                                                            </button>
                                                                        </td>
                                                                    )
                                                                }
                                                                else if (column.value === "actions") {
                                                                    return (
                                                                        <td key={index}>
                                                                            {toView &&
                                                                                <Tooltip title="View">
                                                                                    <RemoveRedEyeIcon className="table-icon pointer" onClick={() => showItems(item)} />
                                                                                </Tooltip>
                                                                            }
                                                                            {toEdit &&
                                                                                <Tooltip title="Edit">
                                                                                    <EditIcon className="table-icon pointer" onClick={() => editItem(item)} />
                                                                                </Tooltip>
                                                                            }
                                                                            {toDelete &&
                                                                                <Tooltip title="Delete">
                                                                                    <DeleteIcon className="table-icon pointer" onClick={() => deleteItem(item)} />
                                                                                </Tooltip>
                                                                            }
                                                                            {userView &&
                                                                                <Tooltip title="Users">
                                                                                    <PeopleAltIcon className="table-icon pointer" onClick={() => userItems(item)} />
                                                                                </Tooltip>
                                                                            }

                                                                            <div >
                                                                                {toRunOrch &&
                                                                                    <Tooltip title="Run">
                                                                                        <InputIcon className="table-icon pointer" onClick={() => showRunOrch(item)} />
                                                                                    </Tooltip>
                                                                                }
                                                                                {toScheduleOrch &&
                                                                                    item._class === 'SCHEDULED' &&
                                                                                    <Tooltip title="RESCHEDULE">
                                                                                        <EventRepeatIcon className="table-icon pointer" onClick={() => { showScheOrch(item, 'RESCHEDULE') }} />
                                                                                    </Tooltip>
                                                                                }
                                                                                {toScheduleOrch &&
                                                                                    item._class === 'INSTANT' &&
                                                                                    <Tooltip title="SCHEDULED">
                                                                                        <EventAvailableIcon className="table-icon pointer" onClick={() => { showScheOrch(item, 'SCHEDULE') }} />
                                                                                    </Tooltip>
                                                                                }
                                                                                {toInvite &&
                                                                                    <Tooltip title="User Invite">
                                                                                        <CreateIcon className="table-icon pointer" onClick={() => userInvite(item)} />
                                                                                    </Tooltip>
                                                                                }
                                                                                {
                                                                                    toDeleteOrch &&
                                                                                    <Tooltip title="Delete">
                                                                                        <DeleteIcon className="table-icon pointer" onClick={() => showDeleteOrch(item)} />
                                                                                    </Tooltip>
                                                                                }
                                                                                {/* {
                                                                                        toDeleteOrch &&
                                                                                        <Tooltip title="Restore">
                                                                                            <SettingsBackupRestoreIcon className="table-icon pointer" onClick={() => showDeleteOrch(item)} />
                                                                                        </Tooltip>
                                                                                    } */}
                                                                            </div>
                                                                        </td>
                                                                    )
                                                                }
                                                            })
                                                        }
                                                    </tr >
                                                )
                                                )
                                            }
                                            {
                                                isPagination && <tr><td colSpan={count}><span className="text-info text-center" >
                                                    {isPagination &&
                                                        <div className="paginationNavbar">
                                                            <Pagination
                                                                className='paginationNav'
                                                                color="primary"
                                                                count={totalPages}
                                                                boudaryCount={0}
                                                                siblingCount={0}
                                                                perPageRecords={perPageRecords}
                                                                page={currentPageToShow}
                                                                onChange={(event, value) => displayPage(value)} sx={{ width: 'auto', height: '10px', boxShadow: "none" }}
                                                            />
                                                        </div>
                                                    }</span> </td></tr>
                                            }
                                        </>
                                    ) : (<>
                                        <tr><td colSpan={count}><span className="text-info text-center">{COMMON_TABLE.NO_RECORD_FOUND} <i className="icofont-edit"></i></span></td></tr></>)
                            }
                        </tbody>
                    }
                </>
            ) : (
                <tr>
                    <td colSpan={count}><Loader /></td></tr>
            )
            }
            {
                modalShow && (<>
                    <Modal show={modalShow} onHide={handleClose} className="custom-modal">
                        <Modal.Header closeButton>
                            <Modal.Title>Assign Task to Team/Member</Modal.Title>
                        </Modal.Header>
                        <Modal.Body>
                            <Form onSubmit={submitAssignTask}>
                                <ul>
                                    <li className="mb-3" >
                                        <Form.Group as={Col} controlId="title" >
                                            <Form.Label>Task</Form.Label>
                                            <Form.Control
                                                type="text"
                                                defaultValue={issueDetail._title}
                                                disabled
                                            />
                                        </Form.Group>
                                    </li>
                                    <li className="mb-3">
                                        <Form.Group as={Col} controlId="location">
                                            <Form.Label>Location</Form.Label>
                                            <Form.Control
                                                type="text"
                                                defaultValue={issueDetail._location.name}
                                                disabled
                                            />
                                        </Form.Group>
                                    </li>
                                    <li className="mb-3">
                                        <Form.Group as={Col} controlId="member"
                                        >
                                            <Form.Label>Select Member/Team</Form.Label>
                                            <Form.Control as="select" value={member} onChange={handleSelectChange} defaultValue={member} style={{ height: "32px", fontSize: "14px" }} isInvalid={!member && spinner}>
                                                <option value="" disabled>Select Member...</option>
                                                {userItems.map((member) => {
                                                    return <>
                                                        <option key={member._id} value={member._id}>{member._firstname} {member._lastname}</option>
                                                    </>
                                                })}
                                            </Form.Control>
                                        </Form.Group>
                                    </li>
                                </ul>
                            </Form>
                        </Modal.Body>
                        <Modal.Footer>
                            {member !== "" ? (<><Button
                                className='buttonComman'
                                onClick={submitAssignTask}
                            > <span>{COMMON_TABLE.ASSIGN}{spinner ? <span className="spinner"></span> : ""}</span>

                            </Button></>) : (<><Button
                                className='buttonComman'
                                disabled
                            >
                                <span>{COMMON_TABLE.ASSIGN}{spinner ? <span className="spinner"></span> : ""}</span>

                            </Button></>)}
                        </Modal.Footer>
                    </Modal>
                </>)
            }
        </>
    )
}

