import _ from "lodash";
import { MODEL_MACHINE_STATUS, TIMEFORMATS } from './constants';
import moment from "moment";
export const formatToIsoString = d => d.toISOString().split(".")[0] + "Z"
export const sum = (a) => a.reduce((c, n) => c + n);
export const mean = (a) => sum(a) / a.length;
export const round = (n, places = 2) => !!n ? +(Math.round(n + "e+" + places) + "e-" + places) : n;

export const navigateToRoute = (routeHandler, path, state) => {
  return routeHandler(path, state);
};

// 3D model color coding implamentation
//====================
export const retriveElementPackageIds = (telemetryReadingsData) => {
  let modelElesWithRunning = [];
  let modelElesWithStopped = [];
  let modelElesWithAuto = []; 
  if (telemetryReadingsData && !_.isEmpty(telemetryReadingsData)) {
    telemetryReadingsData.forEach(telEle => {
      if (telEle?.readings) {
        switch (telEle?.readings['MachineStatus']) {
          case MODEL_MACHINE_STATUS.stopped: //Info: For Stopped Case
            modelElesWithStopped.push(telEle?._modelLocationElementId);
            break;
          case MODEL_MACHINE_STATUS.running://Info: For Running Case
            modelElesWithRunning.push(telEle?._modelLocationElementId);
            break;
            case MODEL_MACHINE_STATUS.auto://Info: For Auto Case
            modelElesWithAuto.push(telEle?._modelLocationElementId);
            break;
          //Info: Increase more cases as per requirements and same we need to update on modelViwer (Comment is here on page for the same)
        }
      }
    })
  }
  return { modelElesWithRunning, modelElesWithStopped,modelElesWithAuto }
}

//Info: Util to publish message on mqtt
export const publishMessageOnMqtt = (client, topic, message) => {
  if (!client || !topic || !message) {
    return 'Something went wrong, Check for client, topic and message!!!';
  }
  // Info: Publish message on client
  const check = client.publish(topic, JSON.stringify(message), { qos: 0 }, (err) => {
    return err ? 'Error on publishing message' : 'Sucsessfully Published';
  })
}

// Info: Create Graphs with initial Fetched records
export const transfFormIntoGraphCollection = (initlaRecords, parametersToCreateGraph) => {
  //Iterate all the readings object inside intial records with map
  let graphsTOCreate = initlaRecords.map(initialR => {
    //Create Graphs array with properties
    let graphsArray = [];
    //Get the keys for for which need to create the graph
    if (!_.isEmpty(initialR.readings)) {
      let readingsKeyToGenerateGraphFor = Object.keys(initialR.readings[0]);
      //create a peroper graph collection with the required set of parameters
      readingsKeyToGenerateGraphFor.forEach(k => {
        // typeof item?.readings[k] !== 'object'
        if (parametersToCreateGraph.includes(k)) {
          graphsArray.push({ graphParameter: k, graphXAxis: _.map(initialR.readings, "_ts").reverse().map(item => moment(item).format(TIMEFORMATS.LOCAL)), graphYAxis: _.map(initialR.readings, k).reverse() })
        }
      })
    }
    return { _sourceId: initialR._sourceId, graphs: graphsArray }
  })
  return graphsTOCreate;

}
//Info: Set New Reading on Graph Array
export const setNewReadingValuesToGraph = (graphArr, readingObj) => {
  if (!graphArr || !readingObj || !_.isArray(graphArr)) {
    console.error('Error: Graph Array or New Reading is undefined or blank!!');
    return false;
  }
  let elementToUpdate = graphArr.map((g) => {
    //Info: g stands for graphElement used as generic
    //Info: Array Elelment to update as per new reading
    if (g._sourceId === readingObj._sourceId) {
      let { graphs } = g;
      //Drill to sub graph elelement
      let newGraphsArr = graphs.map((sg) => {
        //Info: sg stands for subgraph element used as generic
        //Need to push the new timeStamp into graphXAxis
        //Need to chcek the parameter of graph ele and set it get reading of that to push on graphYAxis
        //Pusing timeStamp into graphXAxis
        limitStorage(sg?.graphXAxis, moment(readingObj._ts).format(TIMEFORMATS.LOCAL));
        const checkTypeAndConvertIntoNumber = readingObj[`${sg.graphParameter}`] && typeof readingObj[`${sg.graphParameter}`] !== 'number' ? parseFloat(readingObj[`${sg.graphParameter}`].split(' ')[0]) : readingObj[`${sg.graphParameter}`];
        limitStorage(sg?.graphYAxis, checkTypeAndConvertIntoNumber);
        return sg
      })
      return { ...g, graphs: newGraphsArr }
    }
    return g
  });
  return elementToUpdate;

}

//Info: Limit readings array with First-In-First-Out
export const limitStorage = (storage, newItem) => {
  const limit = process.env.IOT_READINGS_STORE_LIMIT || 50
  if (storage && storage.length >= limit) {
    storage.shift();
  }
  return storage.push(newItem);
}



// Info: CreateNotification Object
const constructNotification = (notificationItem) => {
  return _.has(notificationItem, MODEL_MACHINE_STATUS.anomaly) ? {
    _sourceId: notificationItem?._sourceId,
    _status: notificationItem['MachineStatus'],
    _anomaly: notificationItem[MODEL_MACHINE_STATUS.anomaly],
    _responsibleParamters: [],
    _ts: notificationItem._ts
  } : {
    _sourceId: notificationItem?._sourceId,
    _status: notificationItem['MachineStatus'],
    _ts: notificationItem._ts,
    _anomaly: 'No'
  }
}
export const generateNotification = (newReadings, existingNotifcations) => {
  //Info: 1. First: IF Existing notification is Empty, go with only stopped and anomaly yes 
  //Info: 2. Second if we have item in exiting, go with the current status and anomaly status of the item to remove from notification collection
  try {
    if (_.isEmpty(existingNotifcations)) {
      let filterReadingsWithStopped = !_.isEmpty(newReadings) && newReadings.filter(reading => reading["MachineStatus"] === MODEL_MACHINE_STATUS.stopped || (_.has(reading, MODEL_MACHINE_STATUS.anomaly) && _.isEqual(_.get(reading, MODEL_MACHINE_STATUS.anomaly), MODEL_MACHINE_STATUS.hasAnomaly)));
      if (!_.isEmpty(filterReadingsWithStopped)) {
        let notifications = filterReadingsWithStopped.map(nEle => {
          return constructNotification(nEle);
        });
        return notifications;
      }
      return [];
    } else {
      let filterdWithNewStatus = !_.isEmpty(newReadings) && newReadings.filter(reading => _.has(reading, "MachineStatus") || _.has(reading, MODEL_MACHINE_STATUS.anomaly));
      if (!_.isEmpty(filterdWithNewStatus)) {
        let notifications = filterdWithNewStatus.map(nEle => {
          ;
          return constructNotification(nEle);
        });
        return notifications;
      }
      return [];
    }

  } catch (e) {
    return [];
  }
}

