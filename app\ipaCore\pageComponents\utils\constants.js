export const VALID_FILE_EXT = [".bimpk", ".sgpk", "*"];

export const MISSING_RELATIONS_COLUMNS = [
  {
    Header: "Entity Type",
    width: 100,
    accessor: "EntityType",
    style: {
      textAlign: "center",
    },
    id: "entityType",
  },
  {
    Header: "Name",
    accessor: "Name",
    headerStyle: {
      textAlign: "left",
    },
    id: "name",
  },
];

export const HOME_PAGE = {
  CAT_HEAD: "CAT Digital Twin Experience",
  NO_MODEL: "No Model Available",
};

export const NAME_CONSTANTS = {
  BIM_OPERATIONS_TEXT: "BIM Operations",
  ADD_NEW_FILE_TEXT: "Add New File",
  UPLOADED_MODEL_FILES_TEXT: "Uploaded Model Files",
  BIM_CONTAINER_NAME: "BIM Model Files Conatainer",
  BIM_USER_TYPE: "bimpk_import_ops",
};

export const GRAPH = {
  READING: "Reading",
  TIME: "Time",
  READING_IN: "Reading in",
};

export const SENSOR_MANAGEMENT_SCREEN = {
  SENSOR_MANAGEMENT: "Sensor Management",
  TELEMETRY_COLLECTION: "Telemetry Collection",
  SENSOR_LIST: "Sensor List",
  SEARCH_COLLECTION_NAME: "Search Collection Name...",
  SENSOR_COLLECTION: "Sensor's Collection",
  VIEW_SENSOR: "Sensor Detail",
  ADD_NEW_SENSOR: "Add New Sensor",
  DELETE_ALL_SENSOR: "Delete All Sensor",
  EDIT_SENSOR_ITEM: "Edit Sensor Item",
  ADD_SENSOR_ITEM: "Add Sensor Item",
  SENSOR_NAME: "Sensor Name",
  SENSOR_NAME_IS_REQUIRED: "Sensor Name is Required!",
  SENSOR_TYPE: "Sensor Type",
  SENSOR_TYPE_IS_REQUIRED: "Sensor Type is Required!",
  SENSOR_DESCRIPTION: "Description",
  SENSOR_DESCRIPTION_IS_REQUIRED: "Sensor Description is Required!",
  SENSOR_ID: "Sensor Id",
  SENSOR_ID_IS_REQUIRED: "Sensor Id is Required!",
  UNIT: "Unit",
  SENSOR_UNIT_IS_REQUIRED: "Sensor Unit is Required!",
  SENSOR_ROOM: "Room Location",
  SENSOR_ROOM_IS_REQUIRED: "Sensor Room is Required!",
  THRESHOLD_VALUE: "Threshold Value",
  THRESHOLD_VALUE_IS_REQUIRED: "Threshold Value is Required!",
  EVENT: "Events",
  NO_EVENT_FOUND: "No Events Founds, Please Add Event First!",
  SELECT_EVENT: "Select Event....",
  EVENT_LOCATION: "Event Location",
  LOCATION_IS_REQUIRED: "Location is Required!",
  MODEL_ROOM_NAME: "Room Name",
  MODEL_LOCATION_ID: "Model Location ID",
  SOURCE_ID: "Sensor Source ID (Mechine Source ID)",
};

export const MODEL = {
  CREATE: "Create",
  SAVE_BUTTON: "Save",
  CANCEL: "Cancel",
  CONFIRM: "Confirm",
};

export const COMMON_POPUP_TEXT = {
  TITLE_DELETE: "Delete?",
  TYPE_DELETE: "delete",
  MESSAGE_DELETE: "Are you sure to delete it?",
  SUCCESS: "success",
  DELETE_SUCCESS_MESSAGE: "Deleted Successfully",
  ERROR: "error",
  ERROR_MESSAGE: "Error in deleting",
};

export const TOASTER_MESSAGE = {
  SUCCESS_BTN: "success",
  ERROR_BTN: "error",
  WARNING: "warning",
  SENSOR_IS_IN: "Sensor is in",
  READING_CREATED_SUCCESSFULLY: "Reading Created Successfully!",
  SENSOR_UPDATED_SUCCESSFULLY: "Sensor Updated Successfully!",
  SENSOR_CREATED_SUCCESSFULLY: "Sensor Created Successfully!",
  DELETE_ALL_CREATED_SUCCESSFULLY: "Deleted All Sensors Successfully!",
  DELETE_SUCCESSFULLY: "Deleted Successfully!",
  ERROR_IN_UPDATING: "Error in updating",
  ERROR_IN_CREATING: "Error in Creating",
  ERROR_IN_DELETING: "Error in deleting",
  UPLOAD_SUCCESSFULLY: "File Uploaded Successfully",
  IMPORT_SUCCESSFULLY: "BIM model imported successfully",
  IMPORT_FAILED: "Failed to import BIM model",
  IMPORT_QUEUED: "BIM model import is queued",
  IMPORT_IN_PROGRESS: "BIM model import is in progress",
};

export const TELEMETRY_ITEM_OPERATION = {
  LOCATION: "Location: ",
  FETCH_NEW_READING: "Fetch New Readings",
  READING_HEADING: "Readings",
  FETCHING_READING: "Fetching Reading...",
  ANALYZING_RISK: "Analyzing Risk for sensor...",
  ID: "ID",
  MEASUREMENT: "Measurement",
  TIME: "Time",
  NO_READING_FOUND: "No Readings Available",
};

export const sensors = [];

export const COMMON_TABLE = {
  ASSIGN: "Assign To",
  WITHDRAW: "Withdraw",
  CLOSE: "Close",
  CHANGE_MEMBER_BTN: "Change Member",
  CLOSED: "Closed",
  NA: "NA",
  NO_RECORD_FOUND: "No Records Found",
};

export const SENSOR_STATUS_CLASSES = {
  danger: "dangerbadge",
  "immediate risk": "warningbadge",
  normal: "normalbadge",
};

export const SENSORS = [
  {
    key: "ID",
    value: "_id",
    sort: false,
  },

  {
    key: "Name",
    value: "_name",
    sort: true,
  },
  {
    key: "Item Class",
    value: "_itemClass",
    sort: false,
  },
  {
    key: "Usertype",
    value: "_userType",
    sort: false,
  },
  {
    key: "Actions",
    value: "actions",
    sort: false,
  },
];

export const TEL_ITEM_SENSORS = [
  {
    key: "ID",
    value: "_id",
    sort: false,
  },

  {
    key: "Name",
    value: "_name",
    sort: true,
  },
  {
    key: "Sensor Type",
    value: "_sensorType",
    sort: false,
  },
  {
    key: "Source ID",
    value: "_sourceId",
    sort: false,
  },
  {
    key: "Description",
    value: "_description",
    sort: false,
  },
  {
    key: "Model Package ID",
    value: "_modelLocationElementId",
    sort: false,
  },
  {
    key: "Actions",
    value: "actions",
  },
];

export const DASH_CARDS = {
  DASHBOARD: "Dashboard",
  REDIRECT: "Go To Detail View Page",
  MACHINE_STATUS: "Machine Status",
  WELD_STATE: "Weld State",
  MACHINE_CUTTING_TIME: "Machine Cutting Time",
  DOWNTIME_AIR_CUT: "Downtime Air Cut",
  HYDRAULIC_MOTOR_READING: "Hydraulic Motor Reading",
  HYDROSTATIC_PUMP_READING: "Hydrostatic Pump Reading",
  EQUIP_DASH: "Equipment Dashboard",
  EQUIP_PROPERTIES: "Equipment Properties",
  AS_ON: "As on",
  DATE: "Date",
  TIME: "Time",
  NAV_TO_MODEL: "Navigate To Model",
  NO_READ: "No Readings Available",
  MODEL_OVERVIEW: "Model Overview",
  VIEW_MODEL: "View 3D Model",
  MODEL_IMAGE: "Model Image",
  REAL_TIME: "Real-Time Sensor Readings",
  MODEL_COMP: "Model Components",
  MANAGE_MACHINE: "Manage Machines",
  NO_DATA: "No Data Available",
  SELECT_MACHINE: "Select a Machine to Visualize data from..",
  RESOLVE_FAULT: "Resolve Fault",
  AUTO_STMT1:"The machine operates in Auto mode, checking",
  AUTO_STMT2:"for anomalies and stopping if a problem is detected.",
};

export const NO_VERSION_AVAILABLE_TEXT = "No versions available";
export const EXCLUDED_FIELDS = [
  "_ts",
  "_id",
  "_tsMetadata",
  "error",
  "_sourceId",
  "Previous Program Runtime",
];

export const COLORS = [
  "#8884d8",
  "#82ca9d",
  "#ffc658",
  "#ff8042",
  "#0088FE",
  "#00C49F",
  "#FFBB28",
];

export const DataPointsToShow = [
  "MachineStatus",
  "Weld State",
  "Vib-X",
  "Rapid Feed Rate",
  "Spindle Act Speed",
  "Arc Temp",
];

// ===== CAUTION: Don't update random=====
// Info: MODEL COLORGROUP
// If need to add more color groups and update any color or update title, please verify first the impact
export const MODEL_MACHINE_STATUS = {
  stopped: "Stopped",
  running: "Running",
  anomaly: "Anomaly",
  hasAnomaly: "Yes",
  auto: "Auto",
};
export const modelElesColorGroups = [
  {
    groupName: "Mechine Working State",
    colors: [
      {
        color: "#94ff2d",
        opacity: 0.9,
        title: "Running",
        colorName: "Running",
        elementIds: [],
      },
      {
        color: "#e41010",
        opacity: 0.8,
        title: "Stopped",
        colorName: "Stopped",
        elementIds: [],
      },
      {
        color: "#ff9346",
        opacity: 0.8,
        title: "Auto",
        colorName: "Auto",
        elementIds: [],
      }
    ],
  },
];

export const LANDING_PAGE = {
  BANNER_TITLE: "Welcome to Digital Twin",
  BANNER_SUBTITLE:
    "Experience real-time integration, intelligent insights, and seamless control of your industrial models all in one place.",
  LEARN_MORE: "Learn More",
  DEPLOYED_MODELS: "Deployed Models",
  DEPLOYED_MODEL_SUBTEXT:
    "These are your deployed Digital Twin Models, ready for monitoring and management.",
  ADD_NEW_MODEL: "Add New Model",
  NO_MODEL: "No Model Available",
};
export const parametersToCreateGraph = [
  "Arc Temp",
  "Current",
  "Vib-X",
  "Vib-Y",
  "Vib-Z",
  "Rapid Feed Rate",
  "Spindle Act Speed",
  "Spindle Drive Load",
];

// Prameters units
export const ParamEtersUnits = {
  "Downtime - Air Cut": "s",
  "Machine Cutting Time": "s",
  "Previous Program Runtime": "s",
  "Vib-X": "mm/s",
  "Vib-Y": "mm/s",
  "Vib-Z": "mm/s",
  Current: "A",
  "Rapid Feed Rate": "MMPM",
  "Spindle Act Speed": "RPM",
  "Spindle Drive Load": "%",
  "Arc Temp": "C",
};

// Info: Simulatio Ops
export const SIMULATION_OPS = {
  disturbFeed: "DISTURB_FEED",
  createFault: "GENERATE_FAULT",
  resolveFault: "RESOLVE_FAULT",
  autoSystem: "AUTO_SYSTEM",
};

export const SIMULATION_OPS_TEXTCONTENT = {
  SIMULATION_OPERATION: "Simulation Operations",
  DISTURBFEED: "Disturb Feed",
  MACHINE: "Machine",
  MACHINES: "Machines",
  GENERATE_FAULT: "Generate Fault",
  CREATE_FAULT: "Create Fault",
  RESOLVE_FAULT: "Resolve Fault",
  MAKE_SYSTEM_AUTO: "Make System Auto",
  SYSTEM_AUTO: "System Auto",
  CNC_MACHINE: "CNC Machine",
};
export const MODELVIWER_LOADER_TIME = 25000;

export const TIMEFORMATS = {
  UTC: "ddd MMM DD HH:mm:ss [UTC] YYYY",
  LOCAL: "MMMM Do YYYY, h:mm:ss a",
};

export const MODEL_ELELEMTS_PAGE = {
  LAST_UPDATED: "Last Updated",
  MACHINE_STATUS: "Machine Status",
  CLOSED_DETAILED_VIEW: "Close Detail View",
};

export const sourceNames = {
  WELDR1: "Welding Robot-I",
  WELDR2: "Welding Robot-II",
  CNC1: "CNC-I",
  CNC2: "CNC-2",
};

export const NOTIFICATION = {
  ANOMALY_DETECTED: "Anomaly Detected",
  NO_DATA_FOUND: "No Data Found!!",
  STOP_MACHINE: "Stop Machine",
  MACHINE_NAME: "Machine Name : ",
  SPINDLE: "Spindle Act Speed",
  HAS_BEEN_STOP: "has been stopped",
  ANAMOLY_RESOLVED: "Anomaly Resolved",
  TOASTER_DELAY: 3000,
};
