import { <PERSON>riptHelper } from "@invicara/ipa-core/modules/TreeRendererHelper-38d1ae90";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import _ from "lodash";
import { generateNotification, setNewReadingValuesToGraph, transfFormIntoGraphCollection } from "../pageComponents/utils/common-utils";
import { MODEL_MACHINE_STATUS, parametersToCreateGraph } from "../pageComponents/utils/constants";
const initialState = {
    telemetryDevicesWithLatestReading: [],
    isFetchingTelItems: false,
    error: null,
    isFetchingNewReading: false,
    machineGraphs: [],
    machineNotification: []
};

export const fetchAllTelemetryDevices = createAsyncThunk('telOps/fetchEventList ', async (telCollectionId) => {
    const telItems = await ScriptHelper.executeScript('getAllTelDevicesWithCurrentReading', { telCollectionId: '67f79646e792b708e00b707e', limit: process.env.IOT_READINGS_STORE_LIMIT || 50 })//need to update it to get all sensors with last readings
    return telItems;
})

const MQTTOpsSlice = createSlice({
    name: 'telOps/operations',
    initialState,
    reducers: {
        setTelDevicesReadings: (state, action) => {
            state.telemetryDevicesWithLatestReading = action.payload
        },
        pushNewReadingOnGraphCollection: (state, action) => {
            //Info: Action: {_sourceId, readingsObj}
            //Step 1: Transform it as graph collection object 
            //Step 2: Push on the state (using and tranforming array by limitstorage)
            const originalState = JSON.parse(JSON.stringify(state.machineGraphs));
            let updatedCollectionWithNewReading = setNewReadingValuesToGraph(originalState, action.payload);
            state.machineGraphs = updatedCollectionWithNewReading;
        },
        pushNewNotificaition: (state, action) => {
            let currentState = JSON.parse(JSON.stringify(state.machineNotification));
            action.payload.forEach(newItem => {
                const findItemToPush = currentState.find(existingItem => existingItem?._sourceId === newItem?._sourceId && existingItem?._status === newItem?._status);
                const findItemWithOtherStatus = currentState.find(existingItem => existingItem._sourceId === newItem._sourceId);
                if (!findItemToPush && (newItem._status === MODEL_MACHINE_STATUS.stopped || newItem._anomaly === MODEL_MACHINE_STATUS.hasAnomaly)) {
                    currentState.push(newItem)
                } else if (findItemWithOtherStatus && newItem._status === MODEL_MACHINE_STATUS.running && newItem._anomaly !== MODEL_MACHINE_STATUS.hasAnomaly) {//TODO: need to add one more condition to check anomaly
                    _.remove(currentState, item => item._sourceId === newItem._sourceId)
                } 
                // Info:: For future use
                // else if (findItemWithOtherStatus && newItem._status === MODEL_MACHINE_STATUS.stopped && newItem._anomaly === MODEL_MACHINE_STATUS.hasAnomaly) {
                //     _.remove(currentState, item => item._sourceId === newItem._sourceId)
                // }
            })
            state.machineNotification = currentState;
        },
        removeAnomalyNotificationOnManualStop: (state, action) => {
            if (action.payload) {
                state.machineNotification = state.machineNotification.filter(n => n._sourceId !== action.payload._sourceId && n._anomaly !== action.payload._anomaly && n._status !== action.payload._status);
            }
        }
    },
    extraReducers: (builder) => {
        builder.addCase(fetchAllTelemetryDevices.pending, (state) => {
            state.isFetchingTelItems = true
        });
        builder.addCase(fetchAllTelemetryDevices.fulfilled, (state, action) => {
            state.telemetryDevicesWithLatestReading = action.payload?.sensorsWithReadings || [];
            state.machineGraphs = transfFormIntoGraphCollection(action.payload?.sensorsWithLimitRecords || [], parametersToCreateGraph);
            state.isFetchingTelItems = false
            //set notification on inital
            const readingArr = action.payload?.sensorsWithReadings.map((sensor) => sensor.readings);
            state.machineNotification = generateNotification(readingArr, []);
        })
        builder.addCase(fetchAllTelemetryDevices.rejected, (state, action) => {
            state.telemetryDevicesWithLatestReading = [];
            state.error = action.error.message;
            state.isFetchingTelItems = false;
        })
    }
})

export const { setTelDevicesReadings, pushNewReadingOnGraphCollection, pushNewNotificaition, removeAnomalyNotificationOnManualStop } = MQTTOpsSlice.actions;
export default MQTTOpsSlice.reducer;