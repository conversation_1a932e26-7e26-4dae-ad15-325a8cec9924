import { createSlice } from "@reduxjs/toolkit";

const sharedDataSensorSlice = createSlice({
  name: "sharedDataSensorManagement",
  initialState: {
    sensorList: null,
    sensorDetails: null,
  },
  reducers: {
    setSensorList: (state, action) => {
      state.sensorList = action.payload;
    },
    setSensorDetails: (state, action) => {
      state.sensorDetails = action.payload;
    },
  },
});

export const { setSensorList, setSensorDetails } =
  sharedDataSensorSlice.actions;
export default sharedDataSensorSlice.reducer;
