.mui-table-container.MuiDataGrid-root {
  width: 100%;

  .MuiDataGrid-virtualScrollerRenderZone {
    background-color: white;
  }

  .MuiDataGrid-row {
    &.even {
      background-color: #f5f5f5;

      &:hover {
        background-color: rgba(0, 91, 161, 0.2);
      }
    }

    &.odd {
      &:hover {
        background-color: rgba(0, 91, 161, 0.2);
      }
    }
  }

  .MuiTablePagination-root {

    .MuiTablePagination-selectLabel,
    .MuiTablePagination-displayedRows {
      margin-bottom: 0px;
    }
  }

  .toolbar-container {
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 16px;
    border-bottom: 1px solid #e0e0e0;

    .table-title {
      font-size: 20px;
      font-weight: bold;
      color: #373737;
    }

    .flex-spacer {
      flex-grow: 1;
    }

    .search-wrapper {
      padding: 2px 4px;
      display: flex;
      height: 36px;
      align-items: center;
      width: 300px;
      border: 1px solid #e0e0e0;
      background: white;

      .search-input {
        margin-left: 8px;
        flex: 1;

        input {
          padding: 8px;
          font-size: 14px;
        }
      }

      .search-icon-button {
        color: #005ba1;
      }
    }

    .add-button {
      background-color: #005ba1;
      color: white;
      padding: 6px 16px;
      border-radius: 4px;
      margin-left: 12px;

      &:hover {
        background-color: #023b67;
      }
    }
  }
}

.MuiPaper-root {
  .MuiList-root {
    display: grid;
  }
}