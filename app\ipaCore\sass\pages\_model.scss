.model-slider-sec {
    .model-show-card {
        border-radius: $r_medium;
        box-shadow: $primary_shadow;
        min-height: 319px;
        position: $relative;
        max-width: 398px;
        background-color: $white;

        .modeldeteleico {
            position: absolute;
            right: 10px;
            top: 10px;
            cursor: pointer;
            transition: 0.3s ease-in;

            &:hover {
                color: $danger;
            }
        }

        .modelinfo {
            width: 100%;
            min-height: 60px;
            bottom: 0;
            left: 0;
            border-bottom-left-radius: $r_medium;
            border-bottom-right-radius: $r_medium;
            text-align: center;
            color: $primary_blue;
            text-decoration: underline;
            cursor: pointer;
            @include paddingXY(1rem, 1.4rem);
            font-size: $font_18;
        }
    }
}

.selectedfiles-wrp {
    .selectedFile {
        max-width: 200px;
        background-color: $lightnormal;
        border-radius: $r_medium;
        border: 1px solid $primary_blue;
        padding: 0.3rem 0;
        padding-left: 0.5rem;
        padding-right: 0.2rem;
        margin-right: $rootSize;

        &:last-child {
            margin-right: 0;
        }

        .fileico {
            font-size: $font_18;
            color: $charcoalgrey;
        }

        .filename {
            color: $charcoalgrey;
            font-size: $font_18;
            max-width: 112px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-weight: 400;
        }

        .removeselcted-file {
            display: inline-block;
            color: $primary_blue;
            margin-left: auto;
        }
    }
}

.icon-bgcircle {
    @include circle(44px, $lightnormal);
    border: 1px solid #56b8ff2c;
    color: $blue;
}

.modellist-table {

    &>thead>tr>th,
    &>thead>th,
    &>tbody>tr>td {
        &:first-child {
            padding-left: 5rem;
        }

        &:last-child {
            padding-right: 2.5rem;
        }
    }
}

.model-img-sec {
    background-color: $lightnormal;
    max-height: 168px;
    @include xycentered_content;

    img {
        max-width: 220px;
    }
}

.model-content-sec {
    .modelname-head {
        font-size: $font_16;
        font-weight: $font_bold;
        color: $secondary_blue;
        line-height: 1;

        label {
            font-size: $font_14;
            color: $charcoalgrey;
            display: inline-block;
            margin-bottom: 10px;
        }
    }
}

.model-elements-details {
    @include paddingXY(0, 2rem);
}

.normalStateDot {
    color: $blue;
    font-size: $font_12;
}

.dangerStateDot {
    color: $danger;
    font-size: $font_12;
}

.moderateStateDot {
    color: $themeyellow;
    font-size: $font_12;
}

.entitydata {
    border: 1px solid $lightgrey;
    max-height: 273px;
    overflow: auto;

    .table>thead {
        position: sticky;
        top: -1px;
        background-color: $white;
    }
}

.model-dragpanel-heading {
    color: $primary_blue;
    font-size: $font_16;
    text-transform: capitalize;
}

.navigator-modless {
    .top-bar {
        height: auto;
    }

    .toggle {
        transform: unset;
    }
}

.model-viwer-header {
    i {
        font-size: $s_20;
        color: $secondary_blue;
    }
}

.modelcard-body {
    min-height: calc(100vh - 168px);
    position: relative;

}

#webviewer-container {
    position: absolute;
    width: 100%;
    height: 100%;
    padding: 0px;
    margin: 0;

    #viewer-2d-main {
        max-width: 310px;
        width: 100%;
        margin-top: 80px;
        margin-left: 80px;

        .Viewer2d-toolbar-icon {
            padding: 15px;
            background-color: $secondary_blue;
            border: 0;

            img {
                max-width: 25px;
            }

            span {
                display: none;
            }
        }

        .viewer2D-Controls {
            width: 60px;
            background-color: $white;
            border-bottom-left-radius: 10px;

            div:nth-child(2) {
                flex-direction: column;
            }

            .Viewer_button {
                background: $white !important;

                &:last-child {
                    border-bottom-left-radius: 10px;

                    .Viewer2d-toolbar-icon {
                        border-bottom-left-radius: 10px;
                    }
                }
            }

            & select {
                height: 49px;
                border-radius: 2px;
                font-size: 13px;
                margin-bottom: 20px;
                width: 310px;
                padding: 9px 16px;
                border-radius: 4px;
                position: absolute;
                top: -49px;
                left: 0px;
                border-radius: 0;
                border: 0;
                background-color: $devider_border_color;
                border-top-left-radius: 10px;
                border-top-right-radius: 10px;
                appearance: none;
                background-image: url(/app/ipaCore/sass/assets/angle-down-solid.svg);
                background-repeat: no-repeat;
                background-position-x: 94%;
                background-position-y: 55%;
                background-size: 17px;
                font-size: $font_14;
            }
        }

        #viewer2D {
            position: absolute;
            left: 60px;
            top: 0;
            width: 250px;
            height: 100%;

            canvas {
                border-bottom-right-radius: $r_intermediate;
            }
        }
    }

    // #navbar {
    //     width: 60px;
    //     top: -0;
    //     margin: 0;
    //     position: absolute;
    //     height: 100% !important;
    //     padding-top: 0px;
    //     .viewer-toolbar-btn {
    //         background-color: transparent;
    //         padding: 15px;
    //         &:first-child {
    //             margin-top: -10px;
    //         }
    //         .toolbar-txt {
    //             display: none;
    //         }
    //         .toolbar-icon {
    //             img {
    //                 max-width: 25px;
    //             }
    //         }
    //     }
    // }
    #mainViewer {
        width: calc(100% - 60px) !important;
        position: absolute;
        height: 100%;

        #mainViewer-canvas-container {
            width: 100%;
            position: absolute;
            height: 100%;
        }
    }
}

.viewer-toolbar-submenu {
    position: $absolute !important;
    max-width: 60px;
    top: 0 !important;
    right: 60px !important;
    height: 100%;
    background-color: #a0b7d9;
    z-index: 1;
    width: 60px;
    flex-direction: column;

    .viewer-submenu-icon {
        padding: 15px;
        display: flex;
        justify-content: $align_center;
        align-items: $align_center;

        img {
            max-width: 25px;
        }

        span {
            display: none;
        }
    }
}

.addnew-model-txt {
    color: $secondary_blue;
}

.card-disabled {
    background-color: $lightgrey !important;
    cursor: not-allowed;
}

.pointer {
    cursor: pointer;
}

// New as per need, CAT
// ========= new css as per Model
.viewer {
    position: relative;
    height: calc(100vh - 268px);
    background-color: #ffffff;
    box-shadow: 0px 1px 4px rgba(21, 34, 50, 0.0784313725);
    border-radius: 4px;
}

#small {
    position: absolute;
    background-color: $secondary_blue;
    width: 70px;
    top: 0;
    height: 100%;
    overflow-y: auto;

    .viewer-toolbar-btn {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        padding: 10px;
        justify-content: center;
    }
}

// .viewer2D-Controls{
//     position: absolute;
//     height: 100%;
//     z-index: 1;
//     top: 0;
//     left: 0;
//     .Viewer_button{
//         background-color: #a0b7d9;
//     }
// }

.model-info-sidebar,
.simulation-operation-sidebar {
    position: absolute;
    width: 60px;
    height: 60px;
    top: 0;
    left: 0;

    &.drawerOpen {
        width: 360px;
        height: 100%;
        z-index: 1;
        box-shadow: 0px 3px 6px #00000029;
    }

    .drawer {
        background-color: #fffffff5;
        z-index: 98;

        .drawer-toggle {
            top: 0 !important;
            background: $secondary_blue;
            color: $white;
            border-radius: 0;
        }

        .drawer-content {
            display: none;
            height: 0;

            &.drawer-content-open {
                display: block;
                height: auto;
            }
        }

        .drawer-toggle-open {
            top: 0 !important;
            background: transparent;
            color: #023b67;
        }

    }

    .element-details-cell {
        padding: 8px;
    }
}

.simulation-operation-sidebar {
    top: 0px;
}

.readingbox {
    display: flex;
    margin-bottom: 5px;
    border-bottom: 1px solid;
    border-bottom: 1px solid rebeccapurple;
    padding: 10px 0;

    .reading-key {
        font-weight: 500;
        // color: $secondary_blue;
        min-width: 100px;
    }

    .reading-value {
        font-weight: 500;
        color: $secondary_blue;
        margin-left: auto;
    }
}

.telElems-wrapper {
    width: 100%;
    // background-color: #e8e8e8;
    padding: 15px 10px;

    .readingsWrp {
        margin-top: 10px;

        .elementName {
            color: #023b67;
            font-size: 17px;
            margin-bottom: 5px;
            display: block;
        }
    }
}

.reading-taken {
    color: #626833;
    font-style: italic;
    font-weight: 600;
    padding-bottom: 5px;
    display: block;
    border-bottom: 1px solid #000;
    padding-bottom: 10px;
}

.navigator-modless {
    height: auto;
    max-height: 400px;
    overflow-y: auto;
    background-color: #ffffffbf;
    box-shadow: 2px 4px 4px #15223214;;
}

.valueswrp {
    max-height: 260px;
    overflow-y: auto;
    padding-right: 6px;
}

.no-modal-record {
    width: 100%;
    height: calc(100vh - 120px);
    display: flex;
    align-items: center;
    justify-content: center;
}

//Info: Simulation panel
.simulationops-wrapper {
    .drawer-head {
        margin-top: 3px;
        padding: 0 15px;
        color: $secondary_blue;
        font-weight: bold;
    }

    // height: 100vh;
    .operation-tbs {
        padding: 15px;

        .operation-card {
            padding: 15px;
            width: 100%;
            margin-bottom: 15px;

            .formwrap {
                margin-bottom: 15px;

                .checkbox-label {
                    padding: 5px 0;
                    display: flex;

                    span {
                        &:nth-child(2) {
                            margin-left: 5px;
                        }
                    }
                }
            }
        }

    }
}

// === Model Bottom Panel
.status-icon-withstopped {
    @include circle(20px, red);
}
.status-icon-withrunning {
    @include circle(20px, green);
}

.machine-status{
    font-size: $font_18;
}
.automachine-info{
    color: $secondary_blue;
    font-size: $font_16;
    font-style: italic;
}