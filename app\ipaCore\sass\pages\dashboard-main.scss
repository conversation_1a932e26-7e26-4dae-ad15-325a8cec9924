$primary-color: #373737;
$secondary-color: #ffb300;
.equip-heading {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 44px;
  padding-left: 28px;
  margin-top: 20px;
}
.card-container {
  margin: 0px 41px;
  .overview-container {
    padding: 40px;
    .card2 {
      background-color: #dbe8f5;
    }
    .data-card {
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s;
      color: #373737;
      .heading-container {
        margin-bottom: 22px;
      }
      .equip-time {
        text-align: end;
        margin-top: 6px;
      }
      .MuiCardContent-root {
        min-height: 316px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 15px;
      }
      .data-value {
        font-weight: bold;
        font-size: 1.2rem;
      }
      .data-key {
        font-size: 16px;
        font-weight: 500;
        color: #373737;
        margin-top: 5px;
      }
      .heading {
        text-align: center;
        font-weight: 600;
        color: #373737;
        font-size: 22px;
      }

      .data-change {
        font-size: 16px;
        color: gray;
        margin-top: 50px;
      }
    }
  }
}

.equip-properties {
  margin-bottom: 5px;
  display: flex;
  justify-content: space-between;
}

.drag-wrapper {
  width: 100%;
  margin-top: 37px;
  padding: 0 10px;
  .MuiButton-label{
    color:white !important;
  }
  .drag-ele-name {
    font-size: 17px;
    color: #373737;
    font-weight: 500;
    margin-bottom: 2px;
  }
  .drag-status {
    position: absolute;
    top: 18px;
    font-size: 18px;
    color: #ba5d00;
    .error-icon {
      margin-right: 8px;
      margin-bottom: 3px;
    }
  }
}

.drag-values {
  max-height: 260px;
  overflow-y: auto;
  padding-right: 6px;
  margin-top: 5px;
  .MuiMarkElement-root{
    display:none;
  }
}

.icofont-close{
  font-size:18px;
}
.toggle {
  margin-top:5px;
}

.snackbar-mt{
  margin-top:75px;
}

.MuiSnackbar-root + .MuiSnackbar-root {
  margin-top: 4px !important;
}

.MuiAlert-action{
  display:none !important;
}

.robot-image{
width:100px;
height:100px;
border-radius: 1px;
object-fit: cover;
}

.drag-panel{
  position:relative;
  z-index:1;
}