.title-page {
  margin-top: 25px;
  margin-bottom: 10px;
}

.main-container {
  margin: 17px 32px 0 32px;

  .left-sections {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;

    .left-section1 {
      flex: 0 0 auto;
    }

    .model-image {
      width: 100%;
      object-fit: cover;
      display: block;
      height: auto;
      margin: '0 auto',
    }
  }

  .model-card {
    min-height: 336px;
  }

  .model-head-section {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .model-title {
      font-size: 20px;
      font-weight: bold;
      color: #373737;
    }
  }

  .model-title-graph {
    font-size: 20px;
    font-weight: bold;
    color: #373737;
  }

  .chart-wrapper {
    height: 310;
    border-radius: 1;
    display: 'flex';
    align-items: 'center';
    justify-content: 'center';
    overflow: 'hidden';
    padding: 2;
    flex-direction: 'column';
  }


  .right-section {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .subtitle1 {
    font-size: 20px;
    color: #023b67;
    margin-bottom: 20px;
    font-weight: bold;
  }

  .info1 {
    color: #373737;
    font-size: 16px;
    margin-bottom: 20px;
    font-weight: 700;
  }

  .date-time {
    color: #6e6e6e;
    margin-bottom: 3px;
  }

  .card-scroll {
    height: calc(100vh - 200px);
    overflow-y: auto;
    margin-top: 4px;

    .model-detail-card {
      position: relative;
      margin: 0 0 12px 0;

      .vertical-line {
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 5px;
        border-radius: 4px;
      }

      .running-line {
        background-color: #0f91ef;
      }

      .stopped-line {
        background-color: #c94112;
      }

      :hover {
        transition: transform 0.3s ease-in-out;
        transform: scale(1.02);
      }
    }

    .shadow-selected {
      box-shadow: 0 2px 14px rgba(33, 150, 243, 0.6);
    }
  }

  .no-data {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    font-size: 20px;
    color: #373737;
  }

  .heading-container {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .model-text {
      margin-left: 20px;
    }

    .robot-image {
      object-fit: cover;
      border-radius: 2px;
      width: auto;
    }
  }

  .status-container {
    display: flex;
    margin-top: 16px;

    .red-dot {
      width: 12px;
      height: 12px;
      background-color: #c94112;
      border-radius: 50%;
      display: inline-block;
      margin-top: 15px;
    }

    .blue-dot {
      background-color: #0f91ef;
      margin-left: 25px;
    }

    .yellow-dot {
      background-color: #ffc700;
      margin-left: 25px;
    }

    .checkbox-status {
      margin-left: 7px;
    }
  }
}

/* width */
::-webkit-scrollbar {
  width: 10px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #888;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}