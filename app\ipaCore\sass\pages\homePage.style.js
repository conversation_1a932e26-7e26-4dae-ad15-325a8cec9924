import { styled } from '@mui/material/styles';
import { Card, IconButton, Box, CardMedia, Container } from '@mui/material';

import BannerImage from "@Scss/assets/landing-background.png";

export const HomePageContainers = styled(Container)(({ theme }) => ({
    padding: '0px !important',
    backgroundColor: 'white',
    height: '100vh',
}));

export const PageWrapper = styled(Box)(({ theme }) => ({
    height: '100vh',
    overflowY: 'auto',
    overflowX: 'hidden',
    scrollbarWidth: 'none',
    '&::-webkit-scrollbar': {
        display: 'none'
    },
}));

export const HomePageBannerContainer = styled('div')(({ theme }) => ({
    backgroundImage: `url(${BannerImage})`,
    backgroundRepeat: 'no-repeat',
    backgroundColor: 'transparent',
    backgroundSize: 'cover',
    objectFit: 'cover',
    backgroundPosition: 'center top',
    overflow: 'hidden',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    minHeight: '24rem',
    [theme.breakpoints.up(1900)]: {
        minHeight: '28rem',
    },
    [theme.breakpoints.down('xl')]: {
        minHeight: '18rem',
    },
}));

export const BannerFirstSection = styled(Box)(({ theme }) => ({
    maxWidth: '54.25rem',
    position: 'sticky',
    top: '3.875rem',
    left: '3.375rem',
    [theme.breakpoints.down('xl')]: {
        maxWidth: '40rem',
    },
    [theme.breakpoints.down('lg')]: {
        maxWidth: '35rem',
    }
}));

export const BannerSecondSection = styled(Box)(({ theme }) => ({
    margin: '2.5rem 0 0 3.375rem'
}));

export const BannerTitle = styled('h1')(({ theme }) => ({
    color: 'white',
    fontSize: '2.5rem',
    fontWeight: 'bold',
    marginBottom: '1rem',
    textShadow: '0px 3px 6px #00000029',
    [theme.breakpoints.up(1900)]: {
        fontSize: '3rem',
    },
    [theme.breakpoints.down('lg')]: {
        fontSize: '2rem',
    }
}));

export const BannerSubtitle = styled('h3')(({ theme }) => ({
    color: 'white',
    fontSize: '1.5rem',
    fontWeight: 'normal',
    letterSpacing: '0px',
    marginBottom: 2,
    [theme.breakpoints.down('xl')]: {
        fontSize: '1rem',
    },
    [theme.breakpoints.down('lg')]: {
        fontSize: '1rem',
    }
}));

export const BannerSecondSectionTitle = styled('h2')(({ theme }) => ({
    color: '#023B67',
    fontSize: '2.5rem',
    fontWeight: 'bold',
    marginBottom: '1rem',
    [theme.breakpoints.up(1900)]: {
        fontSize: '3rem',
    },
    [theme.breakpoints.down('lg')]: {
        fontSize: '2rem',
    }
}));

export const BannerSecondSectionSubtitle = styled('h3')(({ theme }) => ({
    color: '#707070',
    fontSize: '1.375rem',
    fontWeight: 'normal',
    letterSpacing: '0px',
    marginBottom: '24px',
    [theme.breakpoints.down('xl')]: {
        fontSize: '1rem',
    },
    [theme.breakpoints.down('lg')]: {
        fontSize: '1rem',
    }
}));

export const CardContainer = styled(Box)(({ theme }) => ({
    margin: '2rem 0 0 3.375rem',
}));

export const CardWrapper = styled(Box)(({ theme }) => ({
    display: 'flex',
    overflowX: 'auto',
    scrollbarWidth: 'none',
    '&::-webkit-scrollbar': {
        display: 'none'
    },
    gap: '4rem',
    paddingTop: '1rem',
    scrollBehavior: 'smooth',
    [theme.breakpoints.down('xl')]: {
        gap: '3rem',
    },
    [theme.breakpoints.down('lg')]: {
        gap: '2rem',
    }
}));

export const StyledCard = styled(Card)(({ theme }) => ({
    maxWidth: '22rem',
    flexShrink: 0,
    backgroundColor: '#F5F5F5',
    paddingBottom: '24px',
    boxShadow: 'none',
    borderRadius: '8px',
    overflow: 'visible',
    position: 'relative',
    cursor: 'pointer',
    '&:hover .arrow-button': {
        backgroundColor: theme.palette.primary.main,
        color: 'white'
    },
    [theme.breakpoints.down('xl')]: {
        maxWidth: '18rem',
    },
    [theme.breakpoints.down('lg')]: {
        maxWidth: '16rem',
    }
}));

export const IconButtonContainer = styled('div')(({ theme }) => ({
    backgroundColor: 'white',
    minWidth: '52px',
    minHeight: '52px',
    borderRadius: '100%',
    position: 'absolute',
    top: '-8px',
    right: '24px',
    [theme.breakpoints.down('xl')]: {
        minWidth: '44px',
        minHeight: '44px',
    },
}));

export const ArrowButton = styled(IconButton)(({ theme }) => ({
    zIndex: 2,
    backgroundColor: '#F5F5F5',
    boxShadow: '0px 2px 4px rgba(0,0,0,0.1)',
    color: theme.palette.primary.main,
    padding: '8px',
    transition: 'all 0.3s ease',
    minWidth: '2.5rem',
    minHeight: '2.5rem',
    top: '6px',
    right: '-6px',
    position: 'relative',
    className: 'arrow-button',
    '&:hover': {
        backgroundColor: theme.palette.primary.main,
        color: 'white'
    },
    [theme.breakpoints.down('xl')]: {
        minWidth: '2rem',
        minHeight: '2rem',
        padding: '6px',
    },
    [theme.breakpoints.down('lg')]: {
        minWidth: '1rem',
        minHeight: '1rem',
    }
}));

export const CardTitle = styled('h3')(({ theme }) => ({
    color: theme.palette.primary.main,
    fontWeight: 'medium',
    fontSize: '1rem',
    marginTop: '8px',
    textTransform: 'capitalize',
    minWidth: '20.31rem',
    [theme.breakpoints.down('xl')]: {
        minWidth: '18rem',
        fontSize: '0.75rem',
    },
    [theme.breakpoints.down('lg')]: {
        minWidth: '16rem',
        fontSize: '0.75rem',
    }
}));

export const CardImage = styled(CardMedia)(({ theme }) => ({
    minHeight: '180px',
    objectFit: 'cover',
    borderBottomLeftRadius: '8px',
    borderBottomRightRadius: '8px',
}));

export const ScrollIconButtonContainer = styled(Box)(({ theme }) => ({
    display: 'flex',
    justifyContent: 'flex-start',
    marginTop: '1rem',
    marginLeft: '1rem',
}));

export const ScrollIconButtonLeft = styled(IconButton)(({ theme }) => ({
    backgroundColor: '#f0f0f0',
    marginRight: '8px',
    '&:hover': {
        backgroundColor: '#e0e0e0'
    }
}));

export const ScrollIconButtonRight = styled(IconButton)(({ theme }) => ({
    backgroundColor: '#f0f0f0',
    '&:hover': {
        backgroundColor: '#e0e0e0'
    }
}));

export const IllustrationImage = styled('img')(({ theme }) => ({
    position: 'absolute',
    right: '2%',
    width: '735px',
    height: '334px',
    top: '34%',
    [theme.breakpoints.up(1900)]: {
        top: '29%',
        width: '63rem',
        height: '29rem',
    },
    [theme.breakpoints.down('xl')]: {
        width: '600px',
        height: '280px',
        top: '34%',
    },
    [theme.breakpoints.down('lg')]: {
        width: '500px',
        height: '250px',
        top: '28%',
    },
    [theme.breakpoints.down('lg')]: {
        width: '500px',
        height: '250px',
        top: '32%',
    }
}));