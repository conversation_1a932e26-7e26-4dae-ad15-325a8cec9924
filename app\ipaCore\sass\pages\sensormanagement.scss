.wrapper {
  .header-filter-settion {
    @include margin($font_30, 0, $font_12, 0);
  }
}

.sensorHeader {
  display: flex;
  @include padding($font_24, $font_24, $font_24, $font_24);
}

.submit-button {
  min-width: 82px;
  height: 40px;
  border: 1px solid $primary_blue;
}

.modal-dialog {
  .modal-content {
    .modal-header {
      border-bottom: none;
      @include padding($font_24, $font_24, $font_24, $font_24);
    }

    .model-body {
      @include padding(0, $font_16, $font_16, $font_16);

      .style_form_with_updated {
        .form-control {
          width: 23.5rem;
        }
      }
    }
  }
}

.modal-button {
  @include margin(24px, 0px, 9px, 0px);
  align-items: $align_left;
}

.headerClass {
  display: flex;
  align-items: $align_center;
  justify-content: flex-start;
}

// telemetryOperations *************************
.secmin-height {
  min-height: calc(100vh - 240px);
}

.readinglist-sec {
  .list-group {
    max-height: calc(100vh - 290px);
    overflow-y: scroll;
    scroll-behavior: smooth;
    scrollbar-width: none;

    &::-webkit-scrollbar {
      display: none;
    }

    gap: 30px;

    .sensorReadinglist {
      width: 100%;
      height: auto;
      border: 1px solid #e0e0e0;
      background: white;
      padding: 16px;
      box-shadow: 16px;
    }

    .list-group-item {
      border: 0;
      border-bottom: none;
      border-radius: 0;
      @include paddingXY(0, 1rem);

      .reading {
        color: $secondary_blue;
        font-size: $font_18;
        font-weight: $font_bold;
      }

      .readingbox {
        display: flex;
        padding-right: 28px;

        .reading-key {
          font-weight: 500;
          min-width: 100px;
        }

        .reading-value {
          font-weight: 500;
          color: $secondary_blue;
          margin-left: auto;
        }
      }
    }
  }
}

.dataCollHeading {
  @include margin(10px, 0px, 10px, -15px);
}