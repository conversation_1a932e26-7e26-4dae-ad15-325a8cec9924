import { createTheme, responsiveFontSizes } from '@mui/material/styles';

// Create base theme with standard breakpoints
const baseTheme = createTheme({
  breakpoints: {
    values: {
      xs: 0,      // Extra small devices (phones)
      sm: 600,    // Small devices (tablets)
      md: 900,    // Medium devices (small laptops)
      lg: 1200,   // Large devices (laptops/desktops)
      xl: 1536,   // Extra large devices (large desktops)
      xxl: 1900   // Extra extra large devices
    },
  },
  palette: {
    primary: {
      main: '#005ba1',
    },
    secondary: {
      main: '#023b67',
    },
  },
  typography: {
    fontFamily: '"Helvetica Neue", Helvetica, Arial, sans-serif',
    h1: {
      fontSize: '2.5rem',
    },
    h2: {
      fontSize: '2rem',
    },
    h3: {
      fontSize: '1.5rem',
    },
    h4: {
      fontSize: '1.25rem',
    },
    h5: {
      fontSize: '1rem',
    },
    subtitle1: {
      fontSize: '1rem',
    },
    body1: {
      fontSize: '0.875rem',
    },
    body2: {
      fontSize: '0.75rem',
    },
  },
});

// Apply responsive font sizes
const theme = responsiveFontSizes(baseTheme);

export default theme;

