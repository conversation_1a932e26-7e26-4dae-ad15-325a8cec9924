$color_1: #006abbed;
$color_2: white;
$color_3: #c71784;
$color_4: #e23827;
$color_5: #000000;
$color_6: #0a0a23;
$color_7: rgba(0, 106, 187, 0.93);
$color_8: #707070;
$background-color_1: #f3f3f3;
$background-color_2: #fff;
$background-color_3: #ddd;
$background-color_4: #808080;
$background-color_5: #474747;

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

#webviewer-container {
  #navbar {
    right: 0px;
    max-height: calc(100vh - 80px);
    overflow-y: auto;
  }
}

.Viewer2d-toolbar-icon {
  text-align: center;
  background-color: $background-color_1;
  padding: 10px;
  border: 2px solid #006abbed;

  img {
    max-width: 40px;
  }
}

.viewer2D-Controls {
  .DropDownList {
    height: 37px;
    border-radius: 2px;
    font-size: 13px;
    margin-bottom: 20px;
    width: 195px;
  }
}

#logo {
  img {
    height: 60px !important;
  }
}

.appName {
  display: block;
  font-size: 25px;
  font-weight: 500;
  color: $color_1 !important;
}

.session-dropdown {
  color: $color_1 !important;

  &:hover {
    .session-options {
      visibility: visible;
      height: 180px !important;
      border-bottom-left-radius: 10px !important;
      margin-right: 5px !important;
      margin-top: 2px !important;
    }
  }

  .session-options {
    top: 80px !important;
    height: 1px !important;
    width: 270px !important;
    background-color: $background-color_4 !important;

    .session-item {
      a {
        font-size: 17px !important;
      }

      &:hover {
        background-color: $background-color_5 !important;
      }
    }
  }
}

.main-nav {
  .nav-group-li {
    .fa-bars {
      color: $color_1 !important;
    }
  }
}

.btn-1 {
  background-image: linear-gradient(to right,
      #f6d365 0%,
      #fda085 51%,
      #f6d365 100%);
}

.btn-2 {
  background-image: linear-gradient(to right,
      #a1c4fd 0%,
      #c2e9fb 51%,
      #a1c4fd 100%);
}

.btn-3 {
  background-image: linear-gradient(to right,
      #84fab0 0%,
      #8fd3f4 51%,
      #84fab0 100%);
}

.modalContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  align-content: center;
  flex-wrap: wrap;
}

.mybtn {
  padding: 5px;
  text-align: center;
  text-transform: uppercase;
  transition: 0.5s;
  background-size: 200% auto;
  color: $color_2;
  box-shadow: 0 0 20px #eee;
  font-size: 15px;
}

.react-timerange-picker__wrapper {
  border: thin solid lightgrey !important;
  padding: 5px 58px 5px 26px !important;
}

.loadingSpinner {
  font-size: 25px;
  animation: spin infinite 2s linear;
}

.fc {
  max-width: 1100px;
  margin: 0 auto;
}

.btn.customBtn {
  padding: 0.1rem 0.75rem;
  font-size: 0.9rem;
}

i {
  padding-right: 0px !important;
}

.icofont-navigation-menu {
  color: $color_3 !important;
}

.navigator-modless-closed {
  display: none;
}

.data-card {
  min-height: 170px;
  border-radius: 16px !important;

  .btn-light {
    height: 33px;
    font-size: 13px;
  }

  .heading {
    color: $color_1;
  }

  .sensorval {
    font-size: 20px;
    font-weight: 600;
    color: $color_3;
  }

  .alerttxt {
    color: $color_4;
  }
}

.navigator-modless {
  min-width: 400px !important;
  min-height: 48vh;
  padding: 15px !important;
  border-radius: 10px !important;
  max-height: 300px;
  background-color: $background-color_2 !important;
}

.entitydata {
  max-height: 200px;
  overflow: auto;
}

.table {
  text-align: center;
}

.selectSpinner {
  animation: spin infinite 2s linear;
  float: right;
  margin: -28px 17px 0px 0;
}

.dropdown {
  display: inline-block;
  position: relative;

  &:hover {
    .dropdown-options {
      display: block;
    }
  }
}

.dropDownBtn {
  border: none;
  border-radius: 5px;
  padding: 15px 30px;
  font-size: 18px;
  cursor: pointer;

  &:hover {
    background-color: $background-color_3;
  }
}

.dropdown-options {
  display: none;
  position: absolute;
  overflow: auto;
  background-color: $background-color_2;
  border-radius: 5px;
  box-shadow: 0px 10px 10px 0px rgba(0, 0, 0, 0.4);

  a {
    display: block;
    color: $color_5;
    padding: 5px;
    text-decoration: none;

    &:hover {
      color: $color_6;
      background-color: $background-color_3;
      border-radius: 5px;
    }
  }
}

.helloDropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 300px;
  z-index: 2;
  border: 1px solid rgba(0, 0, 0, 0.04);
  box-shadow: 0 16px 24px 2px rgba(0, 0, 0, 0.14);
}

.spacetoleft {
  margin: 10px;
}

.expandIcon {
  color: $color_7 !important;
  float: right !important;
  margin: -7px -33px -5px 0px !important;
  transform: rotate(45deg) !important;
}

.ipa-icon-svg {
  width: 30px !important;
  height: 30px !important;
}

.session-options {
  overflow-y: hidden;
}

.backico_btn {
  background: none;
  border: 0;
  padding: 0;
  box-shadow: 0;
  line-height: 0;
  margin: 0;
  display: inline-block;
}

.backBtnRaiseIssue {
  margin: 10px 0px -35px 0px;
}

.errorShow {
  display: flex;
  align-items: flex-end;
  margin: 0 0 0 200px;
}

.labelTicketCharge {
  margin: 7px 0px 0px 10px !important;
}

.mmv {
  width: 100%;
  height: 100vh;
  overflow: hidden;

  #streamingVideo {
    object-fit: cover !important;
  }
}

.infoBar {
  position: absolute !important;
  z-index: 1;
  display: flex;
  margin: 8px 9px;
}

.MuiButton-text {
  padding: 6px 2px !important;
}

.MuiButton-label {
  color: $color_8 !important;
}

.alignLeft {
  text-align: left !important;
}

.makeEllipse {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}