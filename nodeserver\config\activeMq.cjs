const { accessingDataSource, runIOTDataSetterOrchestrator } = require('./twinit.cjs');
let topics = ['hritika11/feeds/weldrobot', 'hritika11/feeds/CNC ']
const connectMQTT = (client, username) => {
    
    client.on('connect', () => {
        console.log('Connected to Adafruit IO');

        // Subscribe to a feed
        client.subscribe(topics, (err) => {
            if (!err) {
                console.log('Subscribed to feed from node', topics);
            }
        });

        // Publish a message
        // client.publish(`${username}/feeds/amt`, 'Hello Adafruit IO');
    });

    // Handle incoming messages
    client.on('message', (topic, message) => {
        console.log(`Received message on ${topic}: ${message.toString()}`);
        runIOTDataSetterOrchestrator(message);
        // accessingDataSource()
    });
}

module.exports = connectMQTT

