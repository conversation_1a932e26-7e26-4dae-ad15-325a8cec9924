// import express from 'express';
// import mqtt from 'mqtt';

const express = require('express');
const mqtt = require('mqtt');
const connectMQTT = require('./config/activeMq.cjs');
const { twinitCnfiguration } = require('./config/twinit.cjs');
const username = 'hritika11';
const password = '********************************';
const key = 'your_key';
const brokerUrl = `mqtt://@io.adafruit.com`;//info: if doesn't work use, mqtts://@io.adafruit.com
const app = express();
const PORT = process.env.PORT || 5000;
const envVar = process.env.APPNAME
//connect mqtt 
const client = mqtt.connect(brokerUrl, { 
    username,// Your Adafruit IO username
    password: password, // Your Adafruit IO key
});
twinitCnfiguration();
connectMQTT(client, username);
// Serve static files from the React app
app.use(express.static('/build'));

// Start the server
app.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`, envVar);
});